﻿using Common.Exceptions;
using Common.Models.Orders.Messaging;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;

namespace Services.Messaging;

public class OrderExchangeConsumer
{
    private readonly ILogger<OrderExchangeConsumer> _logger;
    private readonly IOrderExchangeClient _orderClient;

    public OrderExchangeConsumer(ILogger<OrderExchangeConsumer> logger,
        IOrderExchangeClient orderClient)
    {
        _logger = logger;
        _orderClient = orderClient;
    }

    public void BeginReceive()
    {
        _orderClient.OnMessageQueueOrderCreate += MessageClient_OnMessageReceived;
        _orderClient.OnMessageQueueOrderUpdate += MessageClient_OnMessageReceived;
        _orderClient.OnMessageQueueOrderStatus += MessageClient_OnMessageReceived;
        _orderClient.Connect();
    }

    public void MessageClient_OnMessageReceived(object? sender, OrderMessageBody e)
    {
        try
        {
            // Task.Run(() => _merchantService.CreateMerchantMessageServiceAsync(e.MerchantMessageData)).Wait();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unknown exception thrown when trying to update adapter");
            throw new ServiceException(Errors.UnknownException);
        }
    }
}