﻿using Common.Models.Apex;
using Common.Models.Configuration;
using Geidea.Utils.Messaging.Base;
using System;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IApexGetMerchantClient
    {
        Task<bool> CheckIfExists(MerchantAction merchant, string accessToken, string url, GenericMessage<Common.Models.Merchant> header, ProviderQueueConfigResponseModel providerConfig);
    }
}