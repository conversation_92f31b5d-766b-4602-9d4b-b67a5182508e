﻿using Geidea.Utils.Common;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Diagnostics.CodeAnalysis;

namespace API.Filters;

[ExcludeFromCodeCoverage]
public class CounterpartyHeaderFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters.Add(new OpenApiParameter
        {
            Name = Constants.CounterpartyHeaderName,
            In = ParameterLocation.Header,
            Required = false,
            Example = new OpenApiString(Constants.CounterpartyUae)
        });
    }
}