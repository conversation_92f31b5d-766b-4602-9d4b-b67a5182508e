﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Messaging
{
    public class ApexResponse
    {
        public string? BusinessId { get; set; }
        public string? MID { get; set; }
        public string? OrderId { get; set; }
        public string? OrderNumber { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? ErrorMessage { get; set; }
        public bool IsSuccess { get; set; }
        public string? Counterparty { get; set; }
    }
}