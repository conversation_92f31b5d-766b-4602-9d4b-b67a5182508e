﻿using Common.Models.Apex;
using Common.Services;
using Common.Models.Configuration;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using Services.Helper.Interface;
using Services.Providers.Apex;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net;
using System.Net.Http;

namespace Services.Tests
{
    public class ApexTokenTests
    {
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private readonly IRequestLogService requestLogService = Substitute.For<IRequestLogService>();
        private readonly ILogger<ApexService> logger = Substitute.For<ILogger<ApexService>>();
        private readonly IHttpClientWrapper httpClientWrapper = Substitute.For<IHttpClientWrapper>();
        private IConfiguration configuration;
        private ApexToken token;

        [SetUp]
        public void Setup()
        {
            var configurationBuilder = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
                { "Apex:ClientId", "SomeValue" },
                { "Apex:ClientSecret", "SomeValue" },

           });

            configuration = configurationBuilder.Build();
            token = new ApexToken(logger, requestLogService, configuration, httpClientWrapper);

            var httpContext = Substitute.For<HttpContext>();
            httpContext.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName] = "test-correlation-id";
            contextAccessor.HttpContext.Returns(httpContext);
        }

        [Test]
        public async Task GetToken_Fails()
        {
            // Arrange
            var providerConfig = new ProviderQueueConfigResponseModel
            {
                ProviderId = Guid.Empty,
                APIBaseUrl = "http://example.com",
                MaxRetryCount = 1
            };

            var responseToken = new { access_token = "access_token" };

            var mockResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonConvert.SerializeObject(responseToken))
            };
            httpClientWrapper.SendHttpPostRequest(string.Empty, Arg.Any<HttpContent>(), null)
                .Returns(Task.FromResult(mockResponse));

            var mockCreateResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("{}")
            };
            httpClientWrapper.SendHttpPostRequest(string.Empty, Arg.Any<HttpContent>(), null)
                .Returns(Task.FromResult(mockCreateResponse));

            // Act
            await token.GetTokenAsync(providerConfig,new MerchantAction(), Guid.Empty, "100i989", "Product");

            // Assert
            await requestLogService.Received(1).UpdateRequestLogAsync(Arg.Any<Models.RequestLog>());
        }
    }
}
