﻿using Common.Models;
using Common.Models.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services;

public interface IProviderService
{
    Task<ProviderConfig> GetProviderConfiguration(Guid providerId);
    Task<ProviderConfig> GetProviderConfigurationByCode(string code);
    Task<List<ProviderQueueConfigResponseModel>> GetProviderQueueConfiguration(QueueConfigSearchFilter filter);
    Task<List<ProviderConfig>> GetAllProvidersConfig(ProviderFilters filter);
}