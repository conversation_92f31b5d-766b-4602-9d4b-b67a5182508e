﻿using Common.Constants;
using Common.Models.Configuration;
using Common.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services.Providers;

public class HttpRequestExecutorService : IHttpRequestExecutorService
{
    private readonly HttpClient httpClient;
    private readonly ILogger<HttpRequestExecutorService> logger;
    private readonly IRequestLogService requestLogService;

    public HttpRequestExecutorService(HttpClient httpClient, ILogger<HttpRequestExecutorService> logger, IRequestLogService requestLogService)
    {
        this.httpClient = httpClient;
        this.logger = logger;
        this.requestLogService = requestLogService;
        this.requestLogService = requestLogService;
    }

    public async Task<HttpResponseMessage> ExecuteAsync(Func<HttpRequestMessage> requestMessageFactory, int maxRetryCount)
    {
        var retryPolicy = Policy
       .Handle<TaskCanceledException>() 
        .Or<HttpRequestException>()
        .OrResult<HttpResponseMessage>(response => !response.IsSuccessStatusCode)
        .WaitAndRetryAsync(
            maxRetryCount,
            retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
             onRetry: (outcome, timespan, retryCount, context) =>
             {
                 logger.LogInformation("Retry {RetryCount} at {TimeSpan} due to {Outcome}", retryCount, timespan, outcome.Exception?.Message);
             });


        return await retryPolicy.ExecuteAsync(async () =>
        {
            var requestMessage = requestMessageFactory();
            logger.LogInformation("Executing request...");
            return await httpClient.SendAsync(requestMessage);
        });
    }
}