﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.22.0" />
		<PackageReference Include="Geidea.Utils.HealthChecks" Version="1.1.342" />
		<PackageReference Include="Geidea.Utils.Swagger" Version="1.1.256" />
		<PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
		<PackageReference Include="Microsoft.OpenApi" Version="1.6.15" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.6.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.6.2" />
		<PackageReference Include="System.Net.NameResolution" Version="4.3.0" />
		<PackageReference Include="Geidea.PaymentGateway.ConfigServiceClient" Version="3.1.123" />
		<PackageReference Include="Geidea.Utils.Logging" Version="1.1.279" />
		<PackageReference Include="Geidea.Utils.Migrations" Version="1.0.134" />
		<PackageReference Include="Geidea.Utils.Versioning" Version="1.1.247" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
		<PackageReference Include="Serilog.Formatting.Compact" Version="1.1.0" />
		<PackageReference Include="Serilog.Settings.AppSettings" Version="2.2.2" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
		<PackageReference Include="Serilog.Sinks.Debug" Version="2.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Messaging\Messaging.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>
</Project>
