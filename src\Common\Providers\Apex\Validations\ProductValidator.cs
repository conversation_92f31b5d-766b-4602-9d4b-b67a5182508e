﻿using Common.Exceptions;
using Common.Models;
using Common.Models.Apex;
using Common.Models.Products;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class ProductValidator : AbstractValidator<ProductAction>
{
    public ProductValidator()
    {
        RuleFor(m => m.MerchantId)
          .NotEmpty()
          .WithErrorCode(Errors.MerchantIdRequired.Code)
          .WithMessage(Errors.MerchantIdRequired.Message);
    }

    }