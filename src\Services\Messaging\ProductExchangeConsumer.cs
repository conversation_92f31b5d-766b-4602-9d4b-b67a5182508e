﻿using Common.Exceptions;
using Common.Models.Products.Messaging;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;

namespace Services.Messaging;

public class ProductExchangeConsumer
{
    private readonly ILogger<ProductExchangeConsumer> _logger;
    private readonly IProductExchangeClient _productClient;

    public ProductExchangeConsumer(ILogger<ProductExchangeConsumer> logger,
        IProductExchangeClient productClient)
    {
        _logger = logger;
        _productClient = productClient;
    }

    public void BeginReceive()
    {
        _productClient.OnMessageQueueProductCreate += MessageClient_OnMessageReceived;
        _productClient.OnMessageQueueUpdate += MessageClient_OnMessageReceived;
        _productClient.OnMessageQueueStatus += MessageClient_OnMessageReceived;
        _productClient.OnMessageQueueSendFile += MessageClient_OnMessageReceived;

        _productClient.Connect();
    }

    public void MessageClient_OnMessageReceived(object? sender, ProductMessageBody e)
    {
        try
        {
            // Task.Run(() => _merchantService.CreateMerchantMessageServiceAsync(e.MerchantMessageData)).Wait();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unknown exception thrown when trying to update adapter");
            throw new ServiceException(Errors.UnknownException);
        }
    }
}