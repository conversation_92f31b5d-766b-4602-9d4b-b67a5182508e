﻿using Geidea.Utils.Counterparty.Providers;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.AppConstants;
using Common.Models.Apex;
using Common.Services;
using Geidea.Utils.Messaging;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Newtonsoft.Json;
using System.Net.Http;
using Services.Providers.Apex;
using Common.Models.Configuration;
using Common.Providers.Apex.Validations;
using Common.Constants;
using System.Net;
using AutoMapper;
using Common.Options;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Common.Models;
using Services.Helper.Interface;
using Geidea.Utils.Messaging.Base;
using FluentAssertions.Common;
using Services.Models;

namespace Services.Tests
{
    public class ApexGetMerchantTests
    {
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private IRequestLogService requestLogService = Substitute.For<IRequestLogService>();
        private readonly ILogger<ApexService> logger = Substitute.For<ILogger<ApexService>>();
        private IHttpRequestExecutorService httpRequestExecutorService = Substitute.For<IHttpRequestExecutorService>();
        private IHttpClientWrapper httpClientWrapper = Substitute.For<IHttpClientWrapper>();
        private IRequestHeaders requestHeaders = Substitute.For<IRequestHeaders>();
        private HttpClient httpClient = Substitute.For<HttpClient>();
        private IProviderService providerService = Substitute.For<IProviderService>();
        private IMapper mapper = Substitute.For<IMapper>();
        private IOptionsMonitor<UrlSettings> urlOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
        private IConfiguration configuration;
        private IApexToken token = Substitute.For<IApexToken>();
        private ApexGetMerchantClient apexGetMerchantClient;


        [SetUp]
        public void Setup()
        {
            var configurationBuilder = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
                { "Apex:ClientId", "SomeValue" },
                { "Apex:ClientSecret", "SomeValue" },

           });

            configuration = configurationBuilder.Build();
            apexGetMerchantClient = new ApexGetMerchantClient(httpClient, logger, providerService, requestLogService, mapper, contextAccessor, urlOptions, httpRequestExecutorService, configuration, requestHeaders, httpClientWrapper, token);
            var httpContext = Substitute.For<HttpContext>();
            httpContext.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName] = "test-correlation-id";
            contextAccessor.HttpContext.Returns(httpContext);
        }

        [Test]
        public async Task CheckIfExists_ShouldCreateRequestLog_When_Business_not_Exists()
        {
            var providerConfig = new ProviderQueueConfigResponseModel
            {
                ProviderId = new Guid(),
                APIBaseUrl = "http://example.com",
                MaxRetryCount = 1
            };
            var merchant = new MerchantAction()
            {
                Merchant = new Common.Models.Apex.Merchant()
                {
                    MerchantId = "123",
                }
            };
            var responseToken = new { access_token = "access_token" };

            var mockResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonConvert.SerializeObject(responseToken))
            };
            httpClientWrapper.SendHttpPostRequest(string.Empty, Arg.Any<HttpContent>(), null)
                .Returns(Task.FromResult(mockResponse));

            var mockCreateResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("{}")
            };
            httpClientWrapper.SendHttpPostRequest(string.Empty, Arg.Any<HttpContent>(), null)
                .Returns(Task.FromResult(mockCreateResponse));

            // Act
            bool value = await apexGetMerchantClient.CheckIfExists(merchant, "Token","Url", new GenericMessage<Common.Models.Merchant>(), providerConfig);

            // Assert
            Assert.IsFalse(value);
        }

        [Test]
        public async Task Test_CatchBlock_TokenError()
        {
            // Arrange
            string url = "your-url";
            var merchant = new MerchantAction()
            {
                Merchant = new Common.Models.Apex.Merchant()
                {
                    MerchantId = "123",
                }
            };
            var providerConfig = new ProviderQueueConfigResponseModel
            {
                ProviderId = new Guid(),
                APIBaseUrl = "http://example.com",
                MaxRetryCount = 1
            };
            var mockAccessToken = new TokenResponse { access_token = "new-access-token" };
            httpClientWrapper.SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>())
             .Returns(Task.FromResult(new HttpResponseMessage(HttpStatusCode.Unauthorized)));

            token.GetTokenAsync(Arg.Any<ProviderQueueConfigResponseModel>(), Arg.Any<Common.Models.Apex.MerchantAction>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.FromResult(mockAccessToken));

            // Act
            await apexGetMerchantClient.CheckIfExists(merchant,"NewToken", url, new GenericMessage<Common.Models.Merchant>(), providerConfig); // Replace with actual method being tested

            // Assert
           await requestLogService.Received(1).CreateRequestLogAsync(Arg.Any<RequestLog>());
        }
    }
}

