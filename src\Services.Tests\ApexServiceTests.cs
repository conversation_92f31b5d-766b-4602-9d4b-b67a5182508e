﻿using Geidea.Utils.Counterparty.Providers;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.AppConstants;
using Common.Models.Apex;
using Common.Services;
using Common.Exceptions;
using Geidea.Utils.Messaging;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Newtonsoft.Json;
using System.Net.Http;
using Services.Providers.Apex;
using Common.Models.Configuration;
using Common.Providers.Apex.Validations;
using Common.Constants;
using System.Net;
using AutoMapper;
using Common.Options;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Common.Models;
using Services.Helper.Interface;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.Exceptions;

namespace Services.Tests
{
    public class ApexServiceTests
    {
        private ApexService apex;
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private readonly IRequestLogService requestLogService = Substitute.For<IRequestLogService>();
        private readonly ILogger<ApexService> logger = Substitute.For<ILogger<ApexService>>();
        private readonly IHttpRequestExecutorService httpRequestExecutorService = Substitute.For<IHttpRequestExecutorService>();
        private readonly IHttpClientWrapper httpClientWrapper = Substitute.For<IHttpClientWrapper>();
        private readonly IApexTerminalClient apexTerminal = Substitute.For<IApexTerminalClient>();
        private readonly IRequestHeaders requestHeaders = Substitute.For<IRequestHeaders>();
        private readonly HttpClient httpClient = Substitute.For<HttpClient>();
        private readonly IProviderService providerService = Substitute.For<IProviderService>();
        private readonly IMapper mapper = Substitute.For<IMapper>();
        private readonly IOptionsMonitor<UrlSettings> urlOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
        private IConfiguration configuration;
        private ApexBusinessAccountClient apexBusiness;
        private ApexToken token;
        private ApexGetMerchantClient apexGetMerchantClient;


        [SetUp]
        public void Setup()
        {
            var configurationBuilder = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
                { "Apex:ClientId", "SomeValue" },
                { "Apex:ClientSecret", "SomeValue" },

           });

            configuration = configurationBuilder.Build();
            token = new ApexToken(logger, requestLogService, configuration, httpClientWrapper);
            apexGetMerchantClient = new ApexGetMerchantClient(httpClient, logger, providerService, requestLogService, mapper, contextAccessor, urlOptions, httpRequestExecutorService, configuration, requestHeaders, httpClientWrapper, token);
            apexBusiness = new ApexBusinessAccountClient(httpClient, logger, providerService, requestLogService, mapper, contextAccessor, urlOptions, httpRequestExecutorService, configuration, requestHeaders, httpClientWrapper, token);
            apex = new ApexService(logger, apexTerminal, apexBusiness, apexGetMerchantClient, token);

            var httpContext = Substitute.For<HttpContext>();
            httpContext.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName] = "test-correlation-id";
            contextAccessor.HttpContext.Returns(httpContext);
        }

        [Test]
        public async Task CreateMerchantMessage_ShouldCreateRequestLog_For_Chain_Request()
        {

            // Arrange
            var merchant = new GenericMessage<Common.Models.Merchant>
            {
                Header = new Header()
                {
                    CorrelationId = Guid.Empty,
                },
                Data = new Common.Models.Merchant()
                {
                    MerchantDetails = new Common.Models.MerchantDetails
                    {
                        MID = "***********",
                        MCC = "m",
                        AcquiringLedger = "m",
                        DefaultLanguage = "d",
                        StoreName = "m",
                        BusinessDomain = "m",
                        FoundationDate = DateTime.Now,
                        AlternativeStoreName = "m",
                        MerchantSegment = "m",
                        AlternativeMerchantName = "m",
                        AdditionalTradingInformation = "m",
                        BusinessId = "m",
                        BusinessType = "m",
                        CompanyPhoneNumber = "********",
                        TLExpiryDate = DateTime.Now.AddYears(5),
                        TLIssueDate = DateTime.Now.AddYears(-4),
                        LegalName = "m",
                        Nickname = "m",
                        RegistrationNumber = "m",
                        RiskLevel = "m",
                    },
                    MerchantId = new Guid("b653aa1c-dfd5-44f2-991a-08dcac7645fc"),
                    MerchantLevel = "1",
                    AccountConfig = new Common.Models.MerchantAccountConfig()
                    {
                        MerchantId = Guid.Empty,
                        SalesAgent = 1,
                        DCCProvider = "1",
                        PayoutDay = 0,
                        PayoutBank = "nn",
                        PayoutCapAmount = 0,
                        PayoutSchedule = "1",
                        SettlementCurrency = "USD",
                        AcceptedPaymentMethods = "VSC",
                        IsHsbcMerchant = "1",
                        Bic = "Bank",
                        AccountNumber = "1",
                        BeneficiaryFullName = "1",
                    },
                    MerchantTerminals = new List<Common.Models.MerchantTerminal>()
                {
                    new Common.Models.MerchantTerminal()
                    {
                        TerminalId ="123",
                        TerminalStatus ="01",
                        SerialNumber ="1",
                        Brand ="1",
                        MerchantId ="12",
                        Model ="1eds",
                    },
                    new Common.Models.MerchantTerminal()
                    {
                        TerminalId ="123",
                        TerminalStatus ="01",
                        SerialNumber ="1",
                        Brand ="1",
                        MerchantId ="12",
                        Model ="1eds",
                    },
                },
                    MerchantStatus = "12",
                    MerchantType = "mm",
                    CommissionTypes = new List<MerchantCommissionConfig>()
                {
                    new MerchantCommissionConfig()
                    {
                        ProductCode = "A",
                        MaxCommAmount ="1",
                        MinCommAmount ="0",
                        CommissionType ="12",
                        FlatAmount ="1",
                        IsInterchange ="N",
                        TransactionType ="12",
                        ScaleProfile ="N",
                        PercentageAmount ="1",
                        VatPercentage =1,
                        ValidFrom ="1",
                        ValidTo ="1",
                        Value =0,
                        MerchantId = Guid.Empty }

                }
                }

            };
            var providers = new List<ProviderQueueConfigResponseModel>
            {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/GetMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "CreateTerminal", Entity = "MERCHANT",TargetURL = "/createTerminal" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "GetToken", Entity = "MERCHANT",TargetURL = "/generate/token" },

            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "GetToken", Entity = "Chain",TargetURL = "/generate/token" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Get", Entity = "Chain",TargetURL = "/merchant-inquiry/getMerchantInformation" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createTerminal" },
            };

            var mockTokenResponse = new TokenResponse { access_token = "some_access_token" };

            var responseMessage = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonConvert.SerializeObject(mockTokenResponse))
            };
            httpClientWrapper.SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>())
                .Returns(Task.FromResult(responseMessage));

            // Act
            await apex.CreateMerchant(merchant, providers);

            // Assert
            await requestLogService.Received().CreateRequestLogAsync(Arg.Any<Models.RequestLog>());
        }

        [Test]
        public void CreateMerchantMessage_ShouldThrowValidationException_WhenMerchantIsInvalid()
        {
            // Arrange
            var merchant = new GenericMessage<Common.Models.Merchant>();
            var providerConfig = new List<ProviderQueueConfigResponseModel>()
            {
                new ProviderQueueConfigResponseModel() {ProviderId = Guid.Empty,
                APIBaseUrl = "http://example.com",
                MaxRetryCount = 1}

            };
            var validator = new MerchantValidator();
            var result = validator.Validate(merchant.Data);
            result.Errors.Add(new FluentValidation.Results.ValidationFailure("PropertyName", "ErrorMessage"));

            // Act
            Assert.ThrowsAsync<ValidationException>(async () => await apex.CreateMerchant(merchant, providerConfig));

            // Assert
            Assert.NotNull(result.Errors);
        }

        [Test]
        public void NumericCountryCodeIsReturned()
        {
            var resultUSD = Common.Constants.ISO.CurrencyCodes.NumericCodes.GetValueOrDefault("USD").ToString();
            var resultIND = Common.Constants.ISO.CurrencyCodes.NumericCodes.GetValueOrDefault("INR").ToString();
            Assert.That(resultUSD == "840");
            Assert.That(resultIND == "356");

        }
    }
}
