﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;
using System;

namespace Common.Providers.Apex.Validations;

public class MerchantDetailsValidator : AbstractValidator<MerchantDetails>
{
    public MerchantDetailsValidator()
    {
        RuleFor(x => x.LegalName)
            .NotEmpty()
            .WithErrorCode(Errors.LegalNameRequired.Code)
            .WithMessage(Errors.LegalNameRequired.Message);

        RuleFor(x => x.MCC)
            .MaximumLength(50)
            .WithErrorCode(Errors.MCCMaxLength.Code)
            .WithMessage(Errors.MCCMaxLength.Message);

        RuleFor(x => x.TLIssueDate)
                 .NotEmpty()
                 .WithErrorCode(Errors.TLIssueDateRequired.Code)
                 .WithMessage(Errors.TLIssueDateRequired.Message)
                 .LessThanOrEqualTo(DateTime.UtcNow)
                 .WithErrorCode(Errors.TLIssueDateFuture.Code)
                 .WithMessage(Errors.TLIssueDateFuture.Message)
                 .When(x => x.TLIssueDate.HasValue);

        RuleFor(x => x.TLExpiryDate)
            .NotEmpty()
            .WithErrorCode(Errors.TLExpiryDateRequired.Code)
            .WithMessage(Errors.TLExpiryDateRequired.Message)
            .GreaterThan(x => x.TLIssueDate)
            .WithErrorCode(Errors.TLExpiryDateAfterRegistration.Code)
            .WithMessage(Errors.TLExpiryDateAfterRegistration.Message)
            .When(x => x.TLIssueDate.HasValue);
    }
}