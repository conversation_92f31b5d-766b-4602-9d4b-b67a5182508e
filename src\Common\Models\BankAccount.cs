﻿using System;

namespace Common.Models;

public class BankAccount
{
    public string? CountryCode { get; set; }
    public string? AccountHolderName { get; set; }
    public int? RefBankId { get; set; }
    public Guid? BankCheckId { get; set; }
    public string? City { get; set; }
    public string? IBAN { get; set; }
    public string? Swift { get; set; }
    public string? AccountName { get; set; }
    public string? BankName { get; set; }
    public string? DDReference { get; set; }
    public DateTime ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public string? BankAccountNumber { get; set; }
    public string? Balance { get; set; }
    public string? Currency { get; set; }
    public string? AccountType { get; set; }
}