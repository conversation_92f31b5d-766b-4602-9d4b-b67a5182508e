﻿using NUnit.Framework;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Microsoft.Extensions.Options;
using Messaging;
using Geidea.Utils.Messaging.Base;
using Common.Services;
using Geidea.Utils.Messaging;
using Common.Models.Apex;
using System;
using Newtonsoft.Json;
using RabbitMQ.Client.Events;
using System.Text;
using FluentAssertions;
using Common.Models.Orders;
using Common.Models.Products;

namespace Services.Tests
{
    public class ProductClientTest
    {
        private IProductCreate product = Substitute.For<IProductCreate>();

        private ProductExchangeClient messageClient;
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private readonly ILogger<MessageClient> logger = Substitute.For<ILogger<MessageClient>>();

        public ProductClientTest()
        {
            var options = Substitute.For<IOptionsMonitor<RabbitMqOptions>>();

            messageClient = new ProductExchangeClient(contextAccessor, logger, options, product);
        }

        [Test]
        public void OnMessageReceiving_WhenProductCreateTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Product>
            {
                Data = new Product()
                {
                    MerchantId ="123",
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Product.Create"
            };
            bool productCreatedTrigger = false;
            messageClient.OnMessageQueueProductCreate += (sender, args) =>
            {
                productCreatedTrigger = true;
                args.ProductMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            productCreatedTrigger.Should().BeTrue();
        }

        public void OnMessageReceiving_WhenProductUpdate_Triggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Product>
            {
                Data = new Product()
                {
                   MerchantId = "123",
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Product.Update"
            };
            bool productUpdateTrigger = false;
            messageClient.OnMessageQueueUpdate += (sender, args) =>
            {
                productUpdateTrigger = true;
                args.ProductMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            productUpdateTrigger.Should().BeTrue();
        }

        public void OnMessageReceiving_WhenProductUpdateStatus_Triggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Product>
            {
                Data = new Product()
                {
                    MerchantId = "123",
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Product.UpdateStatus"
            };
            bool producttStatusrigger = false;
            messageClient.OnMessageQueueStatus += (sender, args) =>
            {
                producttStatusrigger = true;
                args.ProductMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            producttStatusrigger.Should().BeTrue();
        }

        public void OnMessageReceiving_WhenProductSendFile_Triggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Product>
            {
                Data = new Product()
                {
                    MerchantId = "123",
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Product.SendFile"
            };
            bool producttSendFiletrigger = false;
            messageClient.OnMessageQueueSendFile += (sender, args) =>
            {
                producttSendFiletrigger = true;
                args.ProductMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            producttSendFiletrigger.Should().BeTrue();
        }
    }
}
