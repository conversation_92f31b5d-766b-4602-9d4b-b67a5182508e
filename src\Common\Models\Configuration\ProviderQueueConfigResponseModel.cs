﻿using System;

namespace Common.Models.Configuration;

public class ProviderQueueConfigResponseModel
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderCode { get; set; } = string.Empty;
    public string Exchange { get; set; } = string.Empty;
    public string Queue { get; set; } = string.Empty;
    public string Sender { get; set; } = string.Empty;
    public string RouteKey { get; set; } = string.Empty;
    public string RequestType { get; set; } = string.Empty;
    public string TargetURL { get; set; } = string.Empty;
    public string Payload { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Entity { get; set; } = string.Empty;
    public bool IsActive { get; set; } = false;
    public string APIBaseUrl { get; set; } = string.Empty;
    public string CounterParty { get; set; } = string.Empty;

    public int MaxRetryCount { get; set; }
}