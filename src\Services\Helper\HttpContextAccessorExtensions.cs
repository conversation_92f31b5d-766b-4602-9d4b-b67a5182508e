﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Helper
{
    public static class HttpContextAccessorExtensions
    {
        public const string CorrelationIdHeaderName = "X-Correlation-ID";

        public static Guid? GetCorrelationId(this IHttpContextAccessor contextAccessor)
        {
            if (contextAccessor?.HttpContext?.Request == null) return null;

            if (!contextAccessor.HttpContext.Request.Headers.ContainsKey(CorrelationIdHeaderName))
                return null;

            var correlationIdValue = contextAccessor?.HttpContext?.Request?.Headers[CorrelationIdHeaderName].ToString();

            if (Guid.TryParse(correlationIdValue, out var correlationId))
                return correlationId;

            return null;
        }
    }
}
