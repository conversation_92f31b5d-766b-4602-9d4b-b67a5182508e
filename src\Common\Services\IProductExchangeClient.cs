﻿using Common.Models.Products.Messaging;
using System;

namespace Common.Services;

public interface IProductExchangeClient
{
    event EventHandler<ProductMessageBody>? OnMessageQueueProductCreate;
    event EventHandler<ProductMessageBody>? OnMessageQueueUpdate;
    event EventHandler<ProductMessageBody>? OnMessageQueueStatus;
    event EventHandler<ProductMessageBody>? OnMessageQueueSendFile;

    void Connect();
}
