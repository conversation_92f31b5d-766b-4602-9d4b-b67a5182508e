﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class BankAccountResponse : BankAccount
{
    public Guid MerchantBankAccountId { get; set; }
    public bool DeletedFlag { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public IReadOnlyCollection<BankAccountMerchant> Merchants { get; set; } = new List<BankAccountMerchant>();
}