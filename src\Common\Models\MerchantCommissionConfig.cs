﻿using System;

namespace Common.Models;

public class MerchantCommissionConfig
{
    public Guid Id { get; set; }
    public Guid MerchantId { get; set; }
    public string? ProductCode { get; set; }
    public string? CommissionType { get; set; }

    /// <summary>
    /// DCC
    /// </summary>
    public string? TransactionType { get; set; }
    public string? PercentageAmount { get; set; }
    public string? FlatAmount { get; set; }

    public DateTime CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; } = string.Empty;

    public decimal Value { get; set; }
    public string? MinCommAmount { get; set; }
    public string? MaxCommAmount { get; set; }
    public int? VatPercentage { get; set; }
    public string? ScaleProfile { get; set; }
    public string? ValidFrom { get; set; }
    public string? ValidTo { get; set; }
    public string? IsInterchange { get; set; }
}