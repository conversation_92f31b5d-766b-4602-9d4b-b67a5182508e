﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using Common.Models.Products;
using Common.Models.Products.Messaging;
using Common.Services;
using Geidea.Utils.Json;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Services;

namespace Messaging;

public class ProductExchangeClient : MessageClient, IProductExchangeClient
{

    private const string ProductCreateRoutingKey = "NexusBridge.Product.Create";
    private const string ProductUpdateRoutingKey = "NexusBridge.Product.Update";
    private const string SendFileRoutingKey = "NexusBridge.Product.SendFile";
    private const string ProductStatusRoutingKey = "NexusBridge.Product.UpdateStatus";
    private readonly IProductCreate productMessages;

    public ProductExchangeClient(IHttpContextAccessor contextAccessor, ILogger<MessageClient> logger, IOptionsMonitor<RabbitMqOptions> rabbitMqOptions, IProductCreate productMessage)
            : base(contextAccessor, logger, rabbitMqOptions,
                new QueueSettings
                {
                    ExchangeName = "NexusBridge.Product.Recieve",
                    QueueName = "NexusBridge.Product.Recieve",
                    Durable = true

                }
                )

    {
        this.productMessages = productMessage;
    }

    public event EventHandler<ProductMessageBody>? OnMessageQueueProductCreate;
    public event EventHandler<ProductMessageBody>? OnMessageQueueUpdate;
    public event EventHandler<ProductMessageBody>? OnMessageQueueStatus;
    public event EventHandler<ProductMessageBody>? OnMessageQueueSendFile;

    public override void OnMessageReciving(object? model, BasicDeliverEventArgs ea)
    {
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            switch (ea.RoutingKey)
            {
                case ProductCreateRoutingKey:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Product>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueProductCreate?.Invoke(this, new ProductMessageBody(resultMessageBody.Data));
                        productMessages.ProductMessageCreator(resultMessageBody);
                        break;
                    }
                case ProductUpdateRoutingKey:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Product>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueUpdate?.Invoke(this, new ProductMessageBody(resultMessageBody.Data));
                        break;
                    }
                case SendFileRoutingKey:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Product>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueStatus?.Invoke(this, new ProductMessageBody(resultMessageBody.Data));
                        break;
                    }
                case ProductStatusRoutingKey:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Product>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueSendFile?.Invoke(this, new ProductMessageBody(resultMessageBody.Data));
                        break;
                    }
                //case MerchantStore:
                //    {
                //        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message,
                //        new JsonSerializerOptions
                //        {
                //            PropertyNameCaseInsensitive = true
                //        });
                //        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                //        OnMessageQueueCreate?.Invoke(this, new MerchantMessageBody(resultMessageBody.Data));
                //        break;
                //    }
                default:
                    {
                        logger.LogError("Invalid routing key.");
                        break;
                    }
            }

            }
        catch (Exception exception)
        {
            logger.LogError($"Message received on queue {queue.QueueName}. " +
                            $"Failed to process it. Error: {exception.Message}");
        }
    }

    public override void Connect()
    {
        base.Connect();
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, ProductCreateRoutingKey);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, ProductUpdateRoutingKey);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, ProductStatusRoutingKey);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, SendFileRoutingKey);

        //Channel.QueueBind(queue.QueueName, queue.ExchangeName, MerchantMeezaReg);
        //Channel.QueueBind(queue.QueueName, queue.ExchangeName, MerchantStore);




        ReciveMessageFromQueue(OnMessageReciving);
    }
}