﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="FluentValidation" Version="11.4.0" />
    <PackageReference Include="Geidea.Messages" Version="2020.8.3.3" />
    <PackageReference Include="Geidea.Messaging" Version="2.2.256" />
    <PackageReference Include="Geidea.Utils.Messaging" Version="1.0.32" />
  </ItemGroup>

</Project>
