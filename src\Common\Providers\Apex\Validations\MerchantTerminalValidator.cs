﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class MerchantTerminalValidator : AbstractValidator<MerchantTerminal>
{
    public MerchantTerminalValidator()
    {
        RuleFor(x => x.TerminalId)
            .NotEmpty()
            .MaximumLength(24)
            .WithErrorCode(Errors.TerminalIdRequired.Code)
            .WithMessage(Errors.TerminalIdRequired.Message);

        RuleFor(x => x.MerchantId)
            .NotEmpty()
            .WithErrorCode(Errors.MerchantIdRequired.Code)
            .WithMessage(Errors.MerchantIdRequired.Message);

        RuleFor(x => x.TerminalStatus)
            .MaximumLength(2)
            .WithErrorCode(Errors.TerminalStatusMaxLength.Code)
            .WithMessage(Errors.TerminalStatusMaxLength.Message);

        RuleFor(x => x.SerialNumber)
         .MaximumLength(8)
         .WithErrorCode(Errors.TerminalStatusMaxLength.Code)
         .WithMessage(Errors.TerminalStatusMaxLength.Message);
    }
}