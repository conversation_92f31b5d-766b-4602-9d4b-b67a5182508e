﻿using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.Tests.ValidationTests;

public class MerchantAccountConfigValidatorTests
{
    private MerchantAccountConfigValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new MerchantAccountConfigValidator();
    }

    [Test]
    public void Should_Not_Have_Error_When_Model_Is_Valid()
    {
        var model = new MerchantAccountConfig
        {
            SettlementCurrency = "USD",
            PayoutSchedule = "Weekly",
            PayoutCapAmount = 1000
        };
        var result = _validator.TestValidate(model);
        result.ShouldNotHaveAnyValidationErrors();
    }
}
