﻿using System;
using Common.Constants;
namespace Common.Models.Orders;

public class Order
{
    public Guid? AgreementId { get; set; }

    public Guid MerchantId { get; set; }

    public Guid StoreId { get; set; }

    public Guid UserId { get; set; }

    public Guid? AddressId { get; set; }

    public string OrderStatus { get; set; } = Models.Orders.OrderStatus.NEW.ToString();

    public string? TrackingNumber { get; set; }

    public string? TrackingUrl { get; set; }

    public string? Shipper { get; set; }

    public DateTime? ShippedDate { get; set; }

    public string? CouponCode { get; set; }

    public string? PaymentMethod { get; set; }

    public string? CompanyRegNo { get; set; }

    public string? SalesName { get; set; }

    public string? SubscriptionPlan { get; set; }

    public string ProjectName { get; set; } = AppConstants.UnassignedProjectName;

    public string? Note { get; set; }

    public string MerchantName { get; set; } = string.Empty;

    public int? Subtotal { get; set; }

    public int? Discount { get; set; }

    public int? VatPercent { get; set; }

    public int? Vat { get; set; }

    public int? Total { get; set; }

    public int? MonthlySubtotal { get; set; }

    public int? MonthlyVat { get; set; }

    public int? MonthlyTotal { get; set; }

    public int? MonthlyDiscount { get; set; }

    public int? YearlySubtotal { get; set; }

    public int? YearlyVat { get; set; }

    public int? YearlyTotal { get; set; }

    public int? YearlyDiscount { get; set; }

    public string? Currency { get; set; }

    public string? DeliveryMethod { get; set; }

    public short? DeliveryDays { get; set; }

    public bool? ProofOfDelivery { get; set; }

    public string? BillPaymentsStatus { get; set; }

    public bool MigrationRequest { get; set; } = false;
}