﻿using Geidea.Utils.Counterparty.Providers;
using NUnit.Framework;
using FluentAssertions;

namespace Services.Tests
{
    public class CounterpartyProviderTests
    {
        private ICounterpartyProvider provider;

        [SetUp]
        public void Setup()
        {
            provider = new CounterpartyProvider();
        }

        [Test]
        public void SetCodeShouldChangeCounterparty()
        {
            provider.SetCode("OriginalValue");
            var firstResponse = provider.GetCode(); 
            firstResponse.Should().BeEquivalentTo("OriginalValue");

            provider.SetCode("UpdatedValue");
            var secondResponse = provider.GetCode();
            secondResponse.Should().BeEquivalentTo("UpdatedValue");
        }
    }
}
    