﻿using Common.Constants;
using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.Tests.ValidationTests;

public class AddressValidatorTests
{
    private AddressValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new AddressValidator(AppConstants.CounterParty.UAE);
    }

    [Test]
    public void Should_Have_Error_When_Governorate_Is_Empty()
    {
        var model = new Address { Governorate = string.Empty };
        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.Governorate)
              .WithErrorCode(Errors.GovernorateRequired.Code);
    }

    [Test]
    public void Should_Have_Error_When_Email_Is_Invalid()
    {
        var model = new Address { Email = "invalid-email" };
        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.Email)
              .WithErrorCode(Errors.ValidEmailRequired.Code);
    }

    [Test]
    public void Should_Have_Error_When_Email_Exceeds_Max_Length()
    {
        var model = new Address { Email = new string('a', 61) + "@example.com" };
        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.Email)
              .WithErrorCode(Errors.EmailMaxLength.Code);
    }

    [Test]
    public void Should_Not_Have_Error_When_Model_Is_Valid()
    {
        var model = new Address
        {
            Street = "123 Main St",
            Governorate = "Central",
            Area = "Downtown",
            Email = "<EMAIL>",
            Country = "CountryName",
            City = "CityName"
        };
        var result = _validator.TestValidate(model);
        result.ShouldNotHaveAnyValidationErrors();
    }
}
