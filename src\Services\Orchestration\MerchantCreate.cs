﻿using Common.Constants;
using Common.Models;
using Common.Models.Configuration;
using Common.Services;
using Geidea.Utils.Common;
using Geidea.Utils.Messaging.Base;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Services.Providers;
using Services.Providers.Apex;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Messaging.Publishers;
using Common.Models.Messaging;
using Newtonsoft.Json;

namespace Services.Orchestration;

public class MerchantCreate : IMerchantCreate
{
    private readonly ILogger<MerchantCreate> logger;

    private readonly IServiceScopeFactory serviceScopeFactory;
    private readonly IProviderService providerService;
    protected IApexResponsePublisherClient apexResponsePublisherClient;

    public MerchantCreate(ILogger<MerchantCreate> logger,
                          IProviderService providerService,
                          IServiceScopeFactory serviceScopeFactory,
                          IApexResponsePublisherClient apexResponsePublisherClient)
    {
        this.logger = logger;
        this.providerService = providerService;
        this.serviceScopeFactory = serviceScopeFactory;
        this.apexResponsePublisherClient = apexResponsePublisherClient;
    }

    public async Task MerchantMessageCreator(GenericMessage<Merchant> merchant)
    {
        try
        {
            logger.LogInformation("Merchant Message Received in Merchant Create Orchestration");

            var apexResponse = new ApexResponse
            {
                BusinessId = merchant.Data.MerchantDetails.BusinessId,
                MID = merchant.Data.MerchantDetails.MID,
                Counterparty = merchant.Data.Counterparty,
                Timestamp = DateTime.UtcNow,
                OrderNumber = merchant.Data.MerchantTerminals.Any() ? merchant.Data.MerchantTerminals.FirstOrDefault()!.OrderNumber : string.Empty,
                OrderId = merchant.Data.MerchantTerminals.Any() ? merchant.Data.MerchantTerminals.FirstOrDefault()!.OrderId : string.Empty

            };

            logger.LogInformation("Apex Response Initialized {response}", JsonConvert.SerializeObject(apexResponse, new JsonSerializerSettings { Formatting = Formatting.Indented }));
            try
            {
                var providers = await providerService.GetProviderQueueConfiguration(new QueueConfigSearchFilter { CounterParty = merchant.Data.Counterparty! });

                logger.LogInformation("Providers Received After Calling Provider Config Service {Providers}", JsonConvert.SerializeObject(providers));

                // fetch apex data
                var apexData = providers.Where(x => x.ProviderCode == AppConstants.Providers.Apex).ToList();

                using var scope = serviceScopeFactory.CreateScope();

                var apexService = scope.ServiceProvider.GetRequiredService<IApexService>();

                logger.LogInformation("Starting Merchant Federation with Apex by Invoke CreateMerchant in Apex Service File");

                Task.Run(() => apexService.CreateMerchant(merchant, apexData)).Wait();

                logger.LogInformation("Merchant federation completed successfully");

                if (merchant.Data.Counterparty == AppConstants.CounterParty.KSA)
                {
                    apexResponse.IsSuccess = true;

                    logger.LogInformation("Publishing Apex Response {response}", JsonConvert.SerializeObject(apexResponse, new JsonSerializerSettings { Formatting = Formatting.Indented }));

                    apexResponsePublisherClient.SendApexResponseMessage(apexResponse);

                    logger.LogInformation("Apex Response with Success Federation message has been published successfully");
                }

            }
            catch (Exception exception)
            {
                if (merchant.Data.Counterparty == AppConstants.CounterParty.KSA)
                {
                    apexResponse.IsSuccess = false;

                    apexResponse.ErrorMessage = exception.Message;

                    logger.LogInformation("Publishing Apex Response {response}", JsonConvert.SerializeObject(apexResponse, new JsonSerializerSettings { Formatting = Formatting.Indented }));

                    apexResponsePublisherClient.SendApexResponseMessage(apexResponse);

                    logger.LogInformation("Apex Response with Failed Federation message has been published successfully");

                }

                logger.LogError("Message failed to process. Error: {ErrorMessage}", exception.Message);
                throw;
            }
        }
        catch (Exception ex)
        {
            logger.LogError("apexResponse Message failed to process. Error: {ErrorMessage}", ex.Message);
            throw;
        }
    }
}