﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class MerchantPersonOfInterest
{
    public Guid MerchantPersonOfInterestId { get; set; }

    public string NationalId { get; set; } = string.Empty;
    public string? InterestEntityType { get; set; }
    public string? Nationality { get; set; }
    public string? Title { get; set; }
    public string? FirstNameAr { get; set; }
    public string? LastNameAr { get; set; }
    public DateTimeOffset? DOB { get; set; }
    public DateTime? IdIssueDate { get; set; }
    public DateTime? IdExpiryDate { get; set; }
    public string OrganizationRole { get; set; } = string.Empty;
    public bool IsPrincipal { get; set; }
    public decimal OwnershipPercentage { get; set; }
    public bool DeletedFlag { get; set; }
    public DateTime ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public Guid? LeadId { get; set; }
    public string? Gender { get; set; }
    public bool PEP { get; set; }
    public bool AdverseMedia { get; set; }
    public int? WathqRelationId { get; set; }
    public string? GsdkUserId { get; set; }
    public string? PassportNo { get; set; }
    public DateTime? PassportIssueDate { get; set; }
    public DateTime? PassportExpirationDate { get; set; }
    public Guid RoleId { get; set; }
    public string? PhonePrefix { get; set; }
    public string? KYCCheck { get; set; }
    public List<PersonOfInterestCheck> PersonCheck { get; set; } = new();
    public List<ShareholderComapnyCheck> ShareholderComapnyCheck { get; set; } = new();
    public PersonOfInterestAddress? Address { get; set; } = null!;

    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Salutation { get; set; }
    public string? ProfessionalTitle { get; set; }
    public string? MobileNumber { get; set; }
    public string? EmailAddress { get; set; }
    public string? DateOfBirth { get; set; }
    public string? Fax { get; set; }
    public string? Website { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public string? PhoneNumber { get; set; }
    public Guid MerchantId { get; set; }
}