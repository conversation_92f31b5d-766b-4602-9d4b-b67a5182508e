﻿using Common.Models;
using Common.Models.Configuration;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services;

public class ProviderService : IProviderService
{
    private readonly HttpClient httpClient;
    private readonly IOptionsMonitor<UrlSettings> urlOptions;
    private readonly ILogger<ProviderService> logger;
    private string ProviderConfigServiceBaseUrl => urlOptions.CurrentValue?.ProviderConfigServiceBaseUrl ?? string.Empty;

    private const string ProviderConfigServiceEndpoint = "/api/v1/ProviderConfig";
    private const string ProviderConfigServiceByCodeEndpoint = "/api/v1/ProviderConfig/code";
    private const string ProviderQueueConfigServiceSearchEndpoint = "/api/v1/ProviderQueueConfig/search";
    private const string ProviderConfigServiceSearchEndpoint = "/api/v1/ProviderConfig/search";
    public ProviderService(HttpClient httpClient, IOptionsMonitor<UrlSettings> urlOptions, ILogger<ProviderService> logger)
    {
        this.httpClient = httpClient;
        this.urlOptions = urlOptions;
        this.logger = logger;
    }

    public async Task<ProviderConfig> GetProviderConfiguration(Guid providerId)
    {
        string uri = $"{ProviderConfigServiceBaseUrl}{ProviderConfigServiceEndpoint}/{providerId}";

        using (logger.BeginScope("GetProviderConfiguration({@uri})", uri))
        {
            logger.LogInformation($"Calling Provider Configuration service to fecth the Provider Configuration information");

            var response = await httpClient.GetAsync(uri);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when Calling Provider Configuration service to fecth the Provider Configuration information. Error was {StatusCode} {@responseBody}",
                 (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<ProviderConfig>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        }
    }

    public async Task<ProviderConfig> GetProviderConfigurationByCode(string code)
    {
        string uri = $"{ProviderConfigServiceBaseUrl}{ProviderConfigServiceByCodeEndpoint}/{code}";

        using (logger.BeginScope("GetProviderConfiguration({@uri})", uri))
        {
            logger.LogInformation($"Calling Provider Configuration service to fecth the Provider Configuration information");

            var response = await httpClient.GetAsync(uri);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when Calling Provider Configuration service to fecth the Provider Configuration information. Error was {StatusCode} {@responseBody}",
                 (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<ProviderConfig>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        }
    }

    public async Task<List<ProviderQueueConfigResponseModel>> GetProviderQueueConfiguration(QueueConfigSearchFilter filter)
    {
        var uri = $"{ProviderConfigServiceBaseUrl}{ProviderQueueConfigServiceSearchEndpoint}";
        var body = new StringContent(JsonConvert.SerializeObject(filter), Encoding.UTF8, "application/json");

        using (logger.BeginScope("GetProviderQueueConfiguration({@uri})", uri))
        {
            logger.LogInformation($"Calling Provider Queue Configuration service to fecth the Provider Queue Configuration information");

            var response = await httpClient.PostAsync(uri, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when Calling Provider Configuration service to fecth the Provider Queue Configuration information. Error was {StatusCode} {@responseBody}",
                 (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<ProviderQueueConfigResponseModel>>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        }
    }

    public async Task<List<ProviderConfig>> GetAllProvidersConfig(ProviderFilters filter)
    {
        var uri = $"{ProviderConfigServiceBaseUrl}{ProviderConfigServiceSearchEndpoint}";
        var body = new StringContent(JsonConvert.SerializeObject(filter), Encoding.UTF8, "application/json");

        using (logger.BeginScope("GetProviderConfiguration({@uri})", uri))
        {
            logger.LogInformation($"Calling Provider Configuration service to fecth the Provider Configuration information");

            var response = await httpClient.PostAsync(uri, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when Calling Provider Configuration service to fecth the Provider Configuration information. Error was {StatusCode} {@responseBody}",
                 (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<ProviderConfig>>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        }
    }
}