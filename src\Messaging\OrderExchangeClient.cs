﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using Common.Models;
using Common.Models.Orders;
using Common.Models.Orders.Messaging;
using Common.Services;
using Geidea.Utils.Json;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Services;

namespace Messaging;

public class OrderExchangeClient : MessageClient, IOrderExchangeClient
{

    private const string OrderCreate = "NexusBridge.Order.Create";
    private const string OrderUpdate = "NexusBridge.Order.Update";
    private const string OrderStatus = "NexusBridge.Order.UpdateStatus";
    private readonly IOrderCreate orderMessages;



    public OrderExchangeClient(IHttpContextAccessor contextAccessor, ILogger<MessageClient> logger, IOptionsMonitor<RabbitMqOptions> rabbitMqOptions, IOrderCreate orderMessages)
            : base(contextAccessor, logger, rabbitMqOptions,
                new QueueSettings
                {
                    ExchangeName = "NexusBridge.Order.Recieve",
                    QueueName = "NexusBridge.Order.Recieve",
                    Durable = true
                })

    {
        this.orderMessages = orderMessages;
       
    }

    public event EventHandler<OrderMessageBody>? OnMessageQueueOrderCreate;
    public event EventHandler<OrderMessageBody>? OnMessageQueueOrderUpdate;
    public event EventHandler<OrderMessageBody>? OnMessageQueueOrderStatus;

    public override void OnMessageReciving(object? model, BasicDeliverEventArgs ea)
    {
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            switch (ea.RoutingKey)
            {
                case OrderCreate:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueOrderCreate?.Invoke(this, new OrderMessageBody(resultMessageBody.Data));
                        orderMessages.OrderMessageCreator(resultMessageBody);

                        break;
                    }
                case OrderUpdate:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueOrderUpdate?.Invoke(this, new OrderMessageBody(resultMessageBody.Data));
                        break;
                    }
                case OrderStatus:
                    {
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        logger.LogInformation("Message received on queue {@QueueName} with MessageBody {@message}", queue.QueueName, message);

                        OnMessageQueueOrderStatus?.Invoke(this, new OrderMessageBody(resultMessageBody.Data));
                        break;
                    }
                default:
                    {
                        logger.LogError("Invalid routing key.");
                        break;
                    }
            }

            }
        catch (Exception exception)
        {
            logger.LogError($"Message received on queue {queue.QueueName}. " +
                            $"Failed to process it. Error: {exception.Message}");
        }
    }

    public override void Connect()
    {
        base.Connect();
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, OrderCreate);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, OrderUpdate);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, OrderStatus);
        ReciveMessageFromQueue(OnMessageReciving);
    }
}