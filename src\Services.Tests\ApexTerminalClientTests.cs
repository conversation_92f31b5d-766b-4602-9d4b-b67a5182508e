﻿using Geidea.Utils.Counterparty.Providers;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.AppConstants;
using Common.Models.Apex;
using Common.Services;
using Geidea.Utils.Messaging;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Newtonsoft.Json;
using System.Net.Http;
using Services.Providers.Apex;
using Common.Models.Configuration;
using Common.Providers.Apex.Validations;
using Common.Constants;
using System.Net;
using AutoMapper;
using Common.Options;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Common.Models;
using Services.Helper.Interface;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.Exceptions;

namespace Services.Tests
{ 

    public class ApexTerminalClientTests
    {
        private ILogger<ApexService> _logger;
        private HttpClient _httpClient;
        private IProviderService _providerService;
        private IRequestLogService _requestLogService;
        private IMapper _mapper;
        private IHttpContextAccessor _contextAccessor;
        private IOptionsMonitor<UrlSettings> _urlOptions;
        private IApexToken _token;
        private IHttpRequestExecutorService _httpRequestExecutorService;
        private IHttpClientWrapper _httpClientWrapper;
        private IRequestHeaders _requestHeaders;
        private IConfiguration _configuration;
        private ApexTerminalClient _client;


        [SetUp]
        public void Setup()
        {
            _logger = Substitute.For<ILogger<ApexService>>();
            _providerService = Substitute.For<IProviderService>();
            _requestLogService = Substitute.For<IRequestLogService>();
            _mapper = Substitute.For<IMapper>();
            _contextAccessor = Substitute.For<IHttpContextAccessor>();
            _urlOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
            _httpRequestExecutorService = Substitute.For<IHttpRequestExecutorService>();
            _httpClientWrapper = Substitute.For<IHttpClientWrapper>();
            _requestHeaders = Substitute.For<IRequestHeaders>();
            _configuration = Substitute.For<IConfiguration>();
            _httpClient = new HttpClient();
            _token = Substitute.For<IApexToken>();
            var configurationBuilder = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
                { "Apex:ClientId", "SomeValue" },
                { "Apex:ClientSecret", "SomeValue" },

           });
            _configuration = configurationBuilder.Build();

            _client = new ApexTerminalClient(
                _httpClient,
                _logger,
                _providerService,
                _requestLogService,
                _mapper,
                _contextAccessor,
                _urlOptions,
                _httpRequestExecutorService,
                _configuration,
                _requestHeaders,
                _httpClientWrapper,
                _token
            );
        }

        private void SetupHttpClientWrapperResponse(HttpResponseMessage response)
        {
            _httpClientWrapper
                .SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>())
                .Returns(Task.FromResult(response));
        }

        [Test]
        public void GetTokenAsync_ShouldThrowPassthroughException_WhenResponseIsUnsuccessful()
        {
            // Arrange
            var providerConfig = new ProviderQueueConfigResponseModel { APIBaseUrl = "https://api.example.com" };
            var productPayload = new ProductAction { /* set necessary properties */ };
            var correlationId = Guid.NewGuid();

            var response = new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent("Error occurred")
            };

            SetupHttpClientWrapperResponse(response);

            // Act & Assert
            //Assert.ThrowsAsync<PassthroughException>(async () => await _client.GetTokenAsync(providerConfig, productPayload, correlationId, mid));
            _logger.Received(2);
        }

        [Test]
        public async Task CreateTerminal_ShouldCreateTerminalSuccessfully()
        {
            // Arrange
            var providerConfig = new List<ProviderQueueConfigResponseModel>
            {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/GetMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "Create", Entity = "PRODUCT",TargetURL = "/createTerminal" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "GetToken", Entity = "MERCHANT",TargetURL = "/generate/token" },

            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "GetToken", Entity = "MERCHANT",TargetURL = "/generate/token" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "Create", Entity = "MERCHANT",TargetURL = "/merchant-onboarding/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/merchant-inquiry/getMerchantInformation" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/merchant-onboarding/createTerminal" },
            };
            var productDetail = new GenericMessage<Common.Models.Products.Product>
            {
                Data = new Common.Models.Products.Product { MerchantId = "12989809" }
            };
            var productPayload = new ProductAction { /* set necessary properties */ };
            var tokenResponse = new TokenResponse { access_token = "test_token" };

            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("{\"result\":\"success\"}")
            };

            SetupHttpClientWrapperResponse(response);

            // Act
            await _client.CreateTerminal(providerConfig, productPayload, "1234", new Guid(), new MerchantAction());

            // Assert
            await _httpClientWrapper
                .Received(1)
                .SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>());

            await _requestLogService
                .Received(1)
                .CreateRequestLogAsync(Arg.Any<Models.RequestLog>());
        }
    }

}
