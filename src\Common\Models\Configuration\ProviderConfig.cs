﻿using System;
using System.Collections.Generic;

namespace Common.Models.Configuration;

public class ProviderConfig
{
    public Guid ProviderId { get; set; }
    public string ApiKey { get; set; } = string.Empty;
    public string ApiSecret { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientKey { get; set; } = string.Empty;
    public string ProviderKey { get; set; } = string.Empty;
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderCode { get; set; } = string.Empty;
    public string APIDomain { get; set; } = string.Empty;
    public string SFTPConfig { get; set; } = string.Empty;
    public string APIBaseUrl { get; set; } = string.Empty;
    public string HeartBeatURL { get; set; } = string.Empty;
    public bool AvailableStatus { get; set; }
    public int MaxRetryCount { get; set; } = 0;
    public int HeartBeatSchedule { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public List<ProviderPropertyConfig> PropertyConfig { get; set; } = new List<ProviderPropertyConfig>();
    public List<ProviderQueueConfig> QueueConfig { get; set; } = new List<ProviderQueueConfig>();
}