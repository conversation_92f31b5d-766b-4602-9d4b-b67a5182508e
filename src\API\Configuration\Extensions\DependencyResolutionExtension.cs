﻿using Common.Models.Configuration;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.HealthChecks;
using Geidea.Utils.Logging;
using Geidea.Utils.Messaging.Base;
using GeideaPaymentGateway.Utils.RabbitMQ;
using GeideaPaymentGateway.Utils.RabbitMQ.Extensions;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Services.Messaging;
using System.Diagnostics.CodeAnalysis;

namespace API.Configuration.Extensions;

[ExcludeFromCodeCoverage]
public static class DependencyResolutionExtension
{
    public static IServiceCollection RegisterDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddStructuredLogging(configuration);
        services.AddHealthChecks()
            .AddCommonHealthChecks()
            .AddRabbitMqHealthChecks(configuration);

        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IMerchantExchangeClient, MerchantExchangeClient>();
        services.AddSingleton<IOrderExchangeClient, OrderExchangeClient>();
        services.AddSingleton<IProductExchangeClient, ProductExchangeClient>();

        services.AddSingleton<MerchantExchangeConsumer>();
        services.AddSingleton<OrderExchangeConsumer>();
        services.AddSingleton<ProductExchangeConsumer>();

        return services;
    }

    public static IServiceCollection RegisterConfigurations(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddOptions();

        services.AddOptions<RabbitMqOptions>().Bind(configuration.GetSection("Default:RabbitMqConfig"));
        services.AddOptions<RabbitMqConfig>().Bind(configuration.GetSection("Default:RabbitMqConfig"));
        services.AddRabbitMqConnectionFactory();

        services.AddOptions<ExceptionOptions>().Bind(configuration.GetSection("ServiceExceptions"));

        services.Configure<MessageClientConfiguration>(configuration.GetSection("MessageClientConfiguration"));

        services.AddHealthCheckServicesAndOptions(configuration);

        return services;
    }
}