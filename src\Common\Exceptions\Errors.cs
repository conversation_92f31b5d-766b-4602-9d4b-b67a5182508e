﻿namespace Common.Exceptions;

public static class Errors
{
    private const string App = "NBA_";

    public static (string Message, string Code) UnknownException => ("Unknown exception thrown.", App + "UnknownException");
    public static (string Message, string Code) StreetRequired => ("Street is required", App + "StreetRequired");
    public static (string Message, string Code) GovernorateMaxLength => ("Governorate length should not be more than 5 characters", App + "GovernorateMaxLength");
    public static (string Message, string Code) InvalidGovernorate => ("Governorate value is invalid or it was not found in reference data", App + "InvalidCommercialLegalType");

    public static (string Message, string Code) GovernorateRequired => ("Governorate is required", App + "GovernorateRequired");
    public static (string Message, string Code) AreaRequired => ("Area is required", App + "AreaRequired");
    public static (string Message, string Code) ValidEmailRequired => ("A valid Email is required", App + "ValidEmailRequired");
    public static (string Message, string Code) ZipRequired => ("Zip is required", App + "ZipRequired");
    public static (string Message, string Code) CountryRequired => ("Country is required", App + "CountryRequired");
    public static (string Message, string Code) CityRequired => ("City is required", App + "CityRequired");
    public static (string Message, string Code) MerchantBankAccountIdRequired => ("MerchantBankAccountId is required", App + "MerchantBankAccountIdRequired");
    public static (string Message, string Code) CountryCodeRequired => ("CountryCode is required", App + "CountryCodeRequired");
    public static (string Message, string Code) CountryCodeLength => ("CountryCode must be exactly 2 characters.", App + "CountryCodeLength");
    public static (string Message, string Code) AccountHolderNameRequired => ("AccountHolderName is required", App + "AccountHolderNameRequired");
    public static (string Message, string Code) IBANRequired => ("IBAN is required", App + "IBANRequired");
    public static (string Message, string Code) SwiftRequired => ("Swift is required", App + "SwiftRequired");
    public static (string Message, string Code) AccountNameRequired => ("AccountName is required", App + "AccountNameRequired");
    public static (string Message, string Code) ValidFromInPast => ("ValidFrom must be in the past.", App + "ValidFromInPast");
    public static (string Message, string Code) SettlementCurrencyRequired => ("SettlementCurrency is required", App + "SettlementCurrencyRequired");
    public static (string Message, string Code) SettlementCurrencyInvalid => ("SettlementCurrency must be a valid ISO currency code.", App + "SettlementCurrencyInvalid");
    public static (string Message, string Code) PayoutScheduleRequired => ("PayoutSchedule is required", App + "PayoutScheduleRequired");
    public static (string Message, string Code) PayoutDayRange => ("PayoutDay must be between 1 and 31.", App + "PayoutDayRange");
    public static (string Message, string Code) PayoutCapAmountPositive => ("PayoutCapAmount must be a positive amount.", App + "PayoutCapAmountPositive");
    public static (string Message, string Code) PayoutMinimumCapPositive => ("PayoutMinimumCap must be a positive amount.", App + "PayoutMinimumCapPositive");
    public static (string Message, string Code) PayoutTransferFeeAmountPositive => ("PayoutTransferFeeAmount must be a positive amount.", App + "PayoutTransferFeeAmountPositive");
    public static (string Message, string Code) ProductCodeRequired => ("ProductCode is required", App + "ProductCodeRequired");
    public static (string Message, string Code) CommissionTypeRequired => ("CommissionType is required", App + "CommissionTypeRequired");
    public static (string Message, string Code) ValueGreaterThanZero => ("Value must be greater than 0.", App + "ValueGreaterThanZero");
    public static (string Message, string Code) LegalNameRequired => ("LegalName is required", App + "LegalNameRequired");
    public static (string Message, string Code) NicknameRequired => ("Nickname is required", App + "NicknameRequired");
    public static (string Message, string Code) RegistrationNumberRequired => ("RegistrationNumber is required", App + "RegistrationNumberRequired");
    public static (string Message, string Code) MunicipalLicenseNumberRequired => ("MunicipalLicenseNumber is required", App + "MunicipalLicenseNumberRequired");
    public static (string Message, string Code) MCCMaxLength => ("MCC must not exceed 50 characters.", App + "MCCMaxLength");
    public static (string Message, string Code) TLIssueDateRequired => ("TLIssueDate is required", App + "TLIssueDateRequired");
    public static (string Message, string Code) TLIssueDateFuture => ("TLIssueDate cannot be in the future.", App + "TLIssueDateFuture");
    public static (string Message, string Code) TLExpiryDateRequired => ("TLExpiryDate is required", App + "TLExpiryDateRequired");
    public static (string Message, string Code) TLExpiryDateAfterRegistration => ("TLExpiryDate must be greater than TLIssueDate.", App + "TLExpiryDateAfterRegistration");
    public static (string Message, string Code) TLExpiryDateWithinTenYears => ("TLExpiryDate must be within ten years of RegistrationDate.", App + "TLExpiryDateWithinTenYears");
    public static (string Message, string Code) ProductCodeError => ("ProductCode is required", App + "ProductCodeError");
    public static (string Message, string Code) DescriptionRequired => ("Description is required", App + "DescriptionRequired");
    public static (string Message, string Code) PaymentMethodRequired => ("PaymentMethod is required", App + "PaymentMethodRequired");
    public static (string Message, string Code) ChargeFrequencyRequired => ("ChargeFrequency is required", App + "ChargeFrequencyRequired");
    public static (string Message, string Code) ProductCategoryRequired => ("ProductCategory is required", App + "ProductCategoryRequired");
    public static (string Message, string Code) ChargeTypeRequired => ("ChargeType is required", App + "ChargeTypeRequired");
    public static (string Message, string Code) ProductTypeRequired => ("ProductType is required", App + "ProductTypeRequired");
    public static (string Message, string Code) ProductGroupRequired => ("ProductGroup is required", App + "ProductGroupRequired");
    public static (string Message, string Code) CounterPartyRequired => ("CounterParty is required", App + "CounterPartyRequired");
    public static (string Message, string Code) DisplayOrderNonNegative => ("DisplayOrder must be greater than or equal to 0.", App + "DisplayOrderNonNegative");
    public static (string Message, string Code) IsProofRequiredRequired => ("IsProofRequired is required", App + "IsProofRequiredRequired");
    public static (string Message, string Code) PhoneNumberRequired => ("Phone Number is required", App + "PhoneNumberRequired");
    public static (string Message, string Code) MerchantIdRequired => ("MerchantId is required.", App + "MerchantIdRequired");
    public static (string Message, string Code) MerchantTypeRequired => ("MerchantType is required.", App + "MerchantTypeRequired");
    public static (string Message, string Code) MerchantTypeLength => ("MerchantType must be between 1 and 3 characters.", App + "MerchantTypeLength");
    public static (string Message, string Code) MerchantStatusRequired => ("MerchantStatus is required.", App + "MerchantStatusRequired");
    public static (string Message, string Code) MerchantStatusLength => ("MerchantStatus must be between 1 and 20 characters.", App + "MerchantStatusLength");
    public static (string Message, string Code) TerminalIdRequired => ("TerminalId is required.", App + "TerminalIdRequired");
    public static (string Message, string Code) TerminalStatusMaxLength => ("TerminalStatus must not exceed 50 characters.", App + "TerminalStatusMaxLength");
    public static (string Message, string Code) BrandMaxLength => ("Brand must not exceed 50 characters.", App + "BrandMaxLength");
    public static (string Message, string Code) ModelMaxLength => ("Model must not exceed 50 characters.", App + "ModelMaxLength");
    public static (string Message, string Code) FirstNameRequired => ("FirstName is required.", App + "FirstNameRequired");
    public static (string Message, string Code) LastNameRequired => ("LastName is required.", App + "LastNameRequired");
    public static (string Message, string Code) FaxRequired => ("Fax is required.", App + "FaxRequired");
    public static (string Message, string Code) NationalIdRequired => ("NationalId is required.", App + "NationalIdRequired");
    public static (string Message, string Code) MobileNumberRequired => ("Mobile Number is required.", App + "MobileNumberRequired");
    public static (string Message, string Code) DOBRequired => ("DOB is required.", App + "DOBRequired");
    public static (string Message, string Code) DOBInPast => ("DOB must be in the past.", App + "DOBInPast");
    public static (string Message, string Code) EmailMaxLength => ("Email length should not be more than 60 characters", App + "EmailMaxLength");

    public static (string Message, string Code) Invalid_Email_Address => ("Invalid User Email address", App + "Invalid_Email_Address");


    public static (string Message, string Code) InvalidPassword => ("Password must have minimum eight characters, at least one uppercase letter, one lowercase letter and one number", App + "InvalidPassword");
    public static (string Message, string Code) General_Email_Empty => ("Email address should not be empty", App + "General_Email_Empty");
    public static (string Message, string Code) General_Phone_Empty => ("Phone number should not be empty", App + "General_Phone_Empty");
    public static (string Message, string Code) General_Key_Empty => ("Key should not be empty", App + "General_Key_Empty");
    public static (string Message, string Code) PhoneNumberLength => ("Phone number should be 8 or 9 Digit", App + "PhoneNumberLength");
    public static (string Message, string Code) PhoneNumberStart => ("Phone number can not start with 0", App + "PhoneNumberStart");

    public static (string Message, string Code) User_Invalid_Phone_Key => ("Invalid phone number verification key", App + "User_Invalid_Phone_Key");
    public static (string Message, string Code) User_Invalid_Email_Address => ("Invalid User Email address", App + "User_Invalid_Email_Address");
    public static (string Message, string Code) Onboarding_Contact_Verification_Failed => ("Contact verification failed Phone number is not confirmed", App + "PhoneNotConfirmed");
    public static (string Message, string Code) Onboarding_InvalidPhoneNumber => ("Invalid phone number format", App + "Onboarding_InvalidPhoneNumber");
    public static (string Message, string Code) Lead_InvalidEgyptNationalId => ("Invalid National Id for Egypt", App + "Lead_InvalidEgyptNationalId");
    public static (string Message, string Code) Catalogues_NoWhitelist => ("No are catalogues whitelisted", App + "Catalogues_NoWhitelist");
    public static (string Message, string Code) NewLeadNotFound => ("No individual lead with status new has been found", App + "NewLeadNotFound");
    public static (string Message, string Code) Lead_No_Phone => ("Lead does not have a phone number", App + "Lead_No_Phone");
    public static (string Message, string Code) Merchant_No_Phone => ("Merchant does not have a phone number", App + "Merchant_No_Phone");
    public static (string Message, string Code) Lead_No_First_Name => ("Lead does not have first name set", App + "Lead_No_First_Name");

    public static (string Message, string Code) User_First_Name_empty => ("User First name should not be empty", App + "User_First_Name_empty");
    public static (string Message, string Code) User_Last_Name_empty => ("User Last name should not be empty", App + "User_Last_Name_empty");
    public static (string Message, string Code) Merchant_No_First_Name => ("Merchant first name should not be empty", App + "Merchant_No_First_Name");
    public static (string Message, string Code) Merchant_No_Last_Name => ("Merchant last name should not be empty", App + "Merchant_No_Last_Name");
    public static (string Message, string Code) Lead_No_Last_Name => ("Lead does not have last name set", App + "Lead_No_Last_Name");
    public static (string Message, string Code) NoLocationForUser => ("User API created a user but didn't send back a user id", App + "NoLocationForUser");
    public static (string Message, string Code) UnexpectedError => ("Unexpected error during the onboarding process", App + "UnexpectedError");
    public static (string Message, string Code) InvalidLeadId => ("Invalid lead id", App + "InvalidLeadId");
    public static (string Message, string Code) LeadAlreadyConverted => ("Lead was already converted", App + "LeadAlreadyConverted");
    public static (string Message, string Code) IncorrectLeadStatus => ("Lead status is incorrect", App + "IncorrectLeadStatus");
    public static (string Message, string Code) Generic_Error => ("An error has occured while processing the request", App + "Generic_Error");
    public static (string Message, string Code) InvalidUserForLead => ("Invalid number of users for specified lead id", App + "InvalidUserForLead");
    public static (string Message, string Code) InvalidRoles => ("No roles were specified", App + "InvalidRoles");
    public static (string Message, string Code) UserIdNotFound => ("User id not found", App + "UserIdNotFound");
    public static (string Message, string Code) LeadIdNotFound => ("Lead id not found", App + "LeadIdNotFound");
    public static (string Message, string Code) PhoneAlreadyUsed => ("A user with this phone number already exists", App + "PhoneAlreadyUsed");
    public static (string Message, string Code) MerchantIndexIsEmpty => ("Index should not be empty", App + "MerchantIndexIsEmpty");
    public static (string Message, string Code) NationalIdIsEmpty => ("National id should not be empty", App + "NationalIdIsEmpty");
    public static (string Message, string Code) GovernorateIsEmpty => ("Governorate should not be empty", App + "GovernorateIsEmpty");
    public static (string Message, string Code) CityIsEmpty => ("City should not be empty", App + "CityIsEmpty");
    public static (string Message, string Code) AreaIsEmpty => ("Area should not be empty", App + "AreaIsEmpty");
    public static (string Message, string Code) Area_length => ("Area length should not be more than 100 characters", App + "Area_length");
    public static (string Message, string Code) AreaNotFromCity => ("This area is not from this city", App + "AreaNotFromCity");
    public static (string Message, string Code) AreaIsInvalid => ("Area is not valid", App + "AreaIsInvalid");
    public static (string Message, string Code) AddressIsEmpty => ("Address should not be empty", App + "AddressIsEmpty");
    public static (string Message, string Code) AddressLength => ("Address length should not be more than 100 characters", App + "AddressLength");
    public static (string Message, string Code) BusinessNameIsEmpty => ("Business name should not be empty", App + "BusinessNameIsEmpty");
    public static (string Message, string Code) EnglishBusinessName => ("Only English text is allowed for business name", App + "EnglishBusinessName");
    public static (string Message, string Code) BusinessDomainIsEmpty => ("Business domain should not be empty", App + "BusinessDomainIsEmpty");
    public static (string Message, string Code) MerchantTypeIsEmpty => ("Merchant type should not be empty", App + "MerchantTypeIsEmpty");
    public static (string Message, string Code) BundleIsEmpty => ("Bundle should not be empty", App + "BundleIsEmpty");
    public static (string Message, string Code) PhoneDuplicatedAtImport => ("Same phone number, it is used for multiple merchants", App + "PhoneDuplicatedAtImport");
    public static (string Message, string Code) CommercialRegistrationTypeIsEmpty => ("Commercial registration type should not be empty", App + "CommercialRegistrationTypeIsEmpty");
    public static (string Message, string Code) CommercialRegistrationLength => ("Commercial registration should have maximum 20 characters", App + "CommercialRegistrationLength");
    public static (string Message, string Code) CommercialRegistrationNumeric => ("Commercial registration should contain only numbers", App + "CommercialRegistrationNumeric");
    public static (string Message, string Code) NationalIdLength => ("National id should have 14 digits", App + "NationalIdLength");
    public static (string Message, string Code) NationalIdNumber => ("National id should contain only numbers", App + "NationalIdNumber");
    public static (string Message, string Code) NationalIdUsed => ("National id already exists", App + "NationalIdUsed");
    public static (string Message, string Code) Iban_Min15 => ("IBAN must have at least 15 characters", App + "Iban_Min15");
    public static (string Message, string Code) Iban_Max32 => ("IBAN must have a maximum of 32 characters", App + "Iban_Max32");
    public static (string Message, string Code) InvalidDeliveryMethod => ("Delivery method must have a maximum length of 50 characters", App + "InvalidDeliveryMethod");
    public static (string Message, string Code) InvalidDeliveryDays => ("Delivery days must have a value between 0 and 99", App + "InvalidDeliveryDays");
    public static (string Message, string Code) InvalidProofOfDelivery => ("Invalid proof of delivery value Only 'Yes', 'No' or empty values are accepted", App + "InvalidProofOfDelivery");
    public static (string Message, string Code) Iban_Invalid => ("IBAN is not valid", App + "Iban_Invalid");
    public static (string Message, string Code) Iban_Match => ("There must be one IBAN in Bank Accounts that matches Accounts IBAN", App + "Iban_Match");
    public static (string Message, string Code) CityIsInvalid => ("City is invalid or it was not found in reference data", App + "CityIsInvalid");
    public static (string Message, string Code) MerchantTypeIsInvalid => ("Merchant type is not valid", App + "MerchantTypeIsInvalid");
    public static (string Message, string Code) BundleIsInvalid => ("Bundle key is not valid", App + "BundleIsInvalid");
    public static (string Message, string Code) NationalIdDuplicatedAtImport => ("Same nationalId it is used for multiple merchants", App + "NationalIdDuplicatedAtImport");
    public static (string Message, string Code) EmailAddressDuplicatedAtImport => ("Same email address it is used for multiple merchants", App + "EmailAddressDuplicatedAtImport");
    public static (string Message, string Code) CommercialRegistrationDuplicatedAtImport => ("Same registration number is used for multiple merchants", App + "CommercialRegistrationDuplicatedAtImport");
    public static (string Message, string Code) CommercialRegistrationExistingLicenseAtImport => ("Same registration number already exists", App + "CommercialRegistrationExistingLicenseAtImport");
    public static (string Message, string Code) InvalidLengthForFlAndBusinessRegistration => ("Fl And Business Registration must have between 8 and 16 characters", App + "InvalidLengthForFlAndBusinessRegistration");
    public static (string Message, string Code) InvalidFormatForFlAndBusinessRegistration => ("Freelancer format validation failed", App + "InvalidFormatForFlAndBusinessRegistration");
    public static (string Message, string Code) BusinessRegistrationMaxLength => ("Business registration cannot have more than 10 characters", App + "BusinessRegistrationMaxLength");
    public static (string Message, string Code) BusinessRegistrationIsEmpty => ("Business registration should not be empty", App + "BusinessRegistrationIsEmpty");
    public static (string Message, string Code) BusinessRegistrationAndCrOrFlShouldBeFilled => ("Busines Registration and CR or FL should be filled", App + "BusinessRegistrationAndCrOrFlShouldBeFilled");
    public static (string Message, string Code) BusinessRegistrationDuplicateAtImport => ("Business registration should not be duplicated", App + "BusinessRegistrationDuplicateAtImport");
    public static (string Message, string Code) BusinessRegistrationNotMatchingCrOrFl => ("Business registration is not matching either Cr or Fl", App + "BusinessRegistrationNotMatchingCrOrFl");
    public static (string Message, string Code) BusinessRegistrationExistingAtImport => ("Same business registration already exists", App + "BusinessRegistrationExistingAtImport");
    public static (string Message, string Code) EmailAlreadyUsed => ("A user with this email already exists", App + "EmailAlreadyUsed");
    public static (string Message, string Code) FirstNameMaxLength => ("First name must have a maximum of 64 characters", App + "FirstNameMaxLength");
    public static (string Message, string Code) LastNameMaxLength => ("Last name must have a maximum of 64 characters", App + "LastNameMaxLength");
    public static (string Message, string Code) AddressMaxLength => ("Address must have a maximum of 32 characters", App + "AddressMaxLength");
    public static (string Message, string Code) BusinessNameMaxLength => ("Business name must have a maximum of 32 characters", App + "BusinessNameMaxLength");
    public static (string Message, string Code) General_CountryPrefix_Empty => ("Country prefix should not be empty", App + "General_CountryPrefix_Empty");
    public static (string Message, string Code) SaudiNationalIdLength => ("National id should have 10 digits", App + "SaudiNationalIdLength");
    public static (string Message, string Code) ValueDuplicateAtImport => ("Same value is used for multiple entries", App + "ValueDuplicateAtImport");
    public static (string Message, string Code) ReferralChannelDoesNotExist => ("Referral channel does not exist", App + "ReferralChannelDoesNotExist");
    public static (string Message, string Code) ErrorImportingLead => ("There was an error while importing the lead", App + "ErrorImportingLead");
    public static (string Message, string Code) General_InvalidCountryPrefix => ("Invalid country prefix format", App + "General_InvalidCountryPrefix");
    public static (string Message, string Code) WrongCountryPrefix => ("Country prefix does not belong to the country", App + "WrongCountryPrefix");
    public static (string Message, string Code) CountryPrefix_Empty => ("Country Prefix should not be empty", App + "CountryPrefix_Empty");
    public static (string Message, string Code) ReferralChannel_Empty => ("Referral channel should not be empty", App + "ReferralChannel_Empty");
    public static (string Message, string Code) ReferralChannelIsInvalid => ("Referral channel is not valid", App + "ReferralChannelIsInvalid");
    public static (string Message, string Code) ReferralChannel_Length => ("Referral channel should not be greater than 32 characters", App + "ReferralChannel_Length");
    public static (string Message, string Code) Channel_Empty => ("Channel should not be empty", App + "Channel_Empty");
    public static (string Message, string Code) Channel_Invalid => ("Channel is invalid or it was not found in reference data", App + "Channel_Invalid");
    public static (string Message, string Code) Channel_Length => ("Channel should not be greater than 3 characters", App + "Channel_Length");
    public static (string Message, string Code) OrganizationIdDuplicatedAtImport => ("Same organizationId it is used for multiple merchants", App + "OrganizationIdDuplicatedAtImport");
    public static (string Message, string Code) TermsAndConditionsNotFound => ("Terms and Conditions document not found", App + "TermsAndConditionsNotFound");
    public static (string Message, string Code) PhoneConfirmationKeyMaxLength => ("Phone key confirmation key length should not be more than 32 characters", App + "PhoneConfirmationKeyMaxLength");
    public static (string Message, string Code) PasswordMaxLength => ("Password length should not be more than 64 characters", App + "PasswordMaxLength");
    public static (string Message, string Code) Password_invalid => ("Password must contains minimum 8 characters, and at least one Upper case letter, One special character except(/) and one digit", App + "Password_invalid");
    public static (string Message, string Code) Password_empty => ("Password should not empty", App + "Password_empty");
    public static (string Message, string Code) ContactInformationMaxLength => ("Contact information length should not be more than 128 characters", App + "ContactInformationMaxLength");
    public static (string Message, string Code) LanguageMaxLength => ("Language length should not be more than 16 characters", App + "LanguageMaxLength");
    public static (string Message, string Code) LeadStatusMaxLength => ("Lead status length should not be more than 32 characters", App + "LeadStatusMaxLength");
    public static (string Message, string Code) BusinessDomainMaxLength => ("Business domain length should not be more than 64 characters", App + "BusinessDomainMaxLength");
    public static (string Message, string Code) MerchantTypeMaxLength => ("Merchant type length should not be more than 32 characters", App + "MerchantTypeMaxLength");
    public static (string Message, string Code) IsPep_invalid => ("IsPep value is invalid", App + "IsPep_invalid");
    public static (string Message, string Code) PhoneNumberMaxLength => ("Phone number length validation failed", App + "PhoneNumberMaxLength");
    public static (string Message, string Code) CountryPrefixMaxLength => ("Country prefix should not more than 16 charcters", App + "CountryPrefixMaxLength");
    public static (string Message, string Code) OwnerFirstNameMaxLength => ("Owner first name length validation failed", App + "OwnerFirstNameMaxLength");
    public static (string Message, string Code) OwnerLastNameMaxLength => ("Owner last name length validation failed", App + "OwnerLastNameMaxLength");
    public static (string Message, string Code) OwnerFirstNameArMaxLength => ("Owner first name in arabic length validation failed", App + "OwnerFirstNameArMaxLength");
    public static (string Message, string Code) OwnerLastNameArMaxLength => ("Owner first name in arabic length validation failed", App + "OwnerLastNameArMaxLength");
    public static (string Message, string Code) OwnerEmailMaxLength => ("Owner email length validation failed", App + "OwnerEmailMaxLength");
    public static (string Message, string Code) LegalNameMaxLength => ("Legal name length validation failed", App + "LegalNameMaxLength");
    public static (string Message, string Code) LegalNameArMaxLength => ("Legal name in arabic length validation failed", App + "LegalNameArMaxLength");
    public static (string Message, string Code) NationalIdMaxLength => ("National id length validation failed", App + "NationalIdMaxLength");
    public static (string Message, string Code) AddressLineMaxLength => ("Address line length validation failed", App + "AddressLineMaxLength");
    public static (string Message, string Code) AddressLineInvalid => ("Address line validation failed", App + "AddressLineInvalid");
    public static (string Message, string Code) CityMaxLength => ("City length should not be more than 32 characters", App + "CityMaxLength");
    public static (string Message, string Code) AreaMaxLength => ("Area length should not be more than 32 characters", App + "AreaMaxLength");
    public static (string Message, string Code) CountryMaxLength => ("Country length should not be more than 32 characters", App + "CountryMaxLength");
    public static (string Message, string Code) UTMMaxLength => ("UTM length validation failed", App + "UTMMaxLength");
    public static (string Message, string Code) CRMLeadIdMaxLength => ("CRM lead id length validation failed", App + "CRMLeadIdMaxLength");
    public static (string Message, string Code) TahakomTransactionIdMaxLength => ("Tahakom transaction id length validation failed", App + "TahakomTransactionIdMaxLength");
    public static (string Message, string Code) SalesIdMaxLength => ("Sales id length validation failed", App + "SalesIdMaxLength");
    public static (string Message, string Code) Nationality_empty => ("Nationality should not be empty", App + "Nationality_empty");
    public static (string Message, string Code) NationalityMaxLength => ("Nationality length validation failed", App + "NationalityMaxLength");
    public static (string Message, string Code) Nationality_invalid => ("Nationality is invalid or it was not found in reference data", App + "Nationality_invalid");
    public static (string Message, string Code) GenderMaxLength => ("Gender length validation failed", App + "GenderMaxLength");
    public static (string Message, string Code) ReferralChannelMaxLength => ("Referral channel length validation failed", App + "ReferralChannelMaxLength");
    public static (string Message, string Code) CRMProductIdMaxLength => ("CRM Product id length validation failed", App + "CRMProductIdMaxLength");
    public static (string Message, string Code) BusinessTypeMaxLength => ("Business type length validation failed", App + "BusinessTypeMaxLength");
    public static (string Message, string Code) RegistrationNumberMaxLength => ("Registration number length should not be more than 30 characters", App + "RegistrationNumberMaxLength");
    public static (string Message, string Code) AccountHolderNameMaxLength => ("Account holder name length should not be more than 64 characters", App + "AccountHolderNameMaxLength");
    public static (string Message, string Code) AccountHolderName_empty => ("Account holder name should not be empty", App + "AccountHolderName_empty");
    public static (string Message, string Code) InvalidNationalId => ("Emirates ID should start with 784", App + "InvalidNationalId");
    public static (string Message, string Code) NationalIdBirthYear => ("Emirates ID second set of 4 digits should be the birth year", App + "NationalIdBirthYear");
    public static (string Message, string Code) InvalidAccountHolderName => ("Invalid account holder name, accept only alphanumeric values", App + "InvalidAccountHolderName");
    public static (string Message, string Code) IBANMaxLength => ("IBAN length should not be more than 50 characters", App + "IBANMaxLength");
    public static (string Message, string Code) InvalidSaudiIban => ("IBAN is not a valid Saudi IBAN", App + "InvalidSaudiIban");
    public static (string Message, string Code) MunicipalLicenseNumberMaxLength => ("Municipal license number length should not be more than 64 characters", App + "MunicipalLicenseNumberMaxLength");
    public static (string Message, string Code) LegalIdMaxLength => ("Legal Id length should not be more than 10 characters", App + "LegalIdMaxLength");
    public static (string Message, string Code) BundleMaxLength => ("Bundle length should not be more than 32 characters", App + "BundleMaxLength");
    public static (string Message, string Code) SmsMessageMaxLength => ("Sms message length should not be more than 512 characters", App + "SmsMessageMaxLength");
    public static (string Message, string Code) InvalidNationalIdFormat => ("National id does not have the correct format", App + "InvalidNationalIdFormat");
    public static (string Message, string Code) InvalidCheckUserExistsResponse => ("Invalid response from user service when checking if exists", App + "InvalidCheckUserExistsResponse");
    public static (string Message, string Code) InvalidSalesPerson => ("This sales id is invalid", App + "InvalidSalesPerson");
    public static (string Message, string Code) SalesIdNotFound => ("Sales id was not found", App + "SalesIdNotFound");
    public static (string Message, string Code) PayoutAggregation_Invalid => ("Payout Aggregation is invalid", App + "PayoutAggregation_Invalid");
    public static (string Message, string Code) BankAccountNumber_Empty => ("Bank Account Number should not be empty", App + "BankAccountNumber_Empty");
    public static (string Message, string Code) BankAccountNumber_Max34 => ("Bank Account Number must have a maximum of 34 characters", App + "BankAccountNumber_Max34");
    public static (string Message, string Code) BankAccountNumber_Max16 => ("Bank Account Number must have a maximum of 16 characters", App + "BankAccountNumber_Max16");
    public static (string Message, string Code) BankAccountNumber_InvalidCounterparty => ("Bank Account Number is not allowed for the current counterparty", App + "BankAccountNumber_Counterparty");
    public static (string Message, string Code) RefBankId_InvalidCounterparty => ("Bank Name Id is not allowed for the current counterparty", App + "RefBankId_Counterparty");
    public static (string Message, string Code) BankAccountNumber_Invalid => ("Bank Account Number is not valid", App + "BankAccountNumber_Invalid");
    public static (string Message, string Code) BankName_Invalid => ("Bank Name is not valid or it was not found in reference data", App + "BankName_Invalid");
    public static (string Message, string Code) SwiftCode_Invalid => ("Swift Code was not found in reference data", App + "SwiftCode_Invalid");
    public static (string Message, string Code) InvalidBundleForReferralChannel => ("Invalid Bundle for Referral Channel", App + "InvalidBundleForReferralChannel");
    public static (string Message, string Code) InvalidAquiringLedger => ("Invalid aquiring ledger", App + "InvalidAquiringLedger");
    public static (string Message, string Code) GsdkUserIdDuplicatedAtImport => ("Same gsdkUserId it's used for multiple merchants", App + "GsdkUserIdDuplicatedAtImport");
    public static (string Message, string Code) LicenseIdExistsError => ("License id already exists Please Contact Geidea for support", App + "LicenseNumberExistsError");
    public static (string Message, string Code) VatNumberIncorrectFormatSaudi => ("VAT number has an incorrect format", App + "VatNumberIncorrectFormatSaudi");
    public static (string Message, string Code) ImportMerchantWithEmptyAcquirer => ("Acquirer is required", App + "RequiredAcquirer");
    public static (string Message, string Code) ImportMerchantWithInvalidAcquirerLength => ("Acquirer length should not be more than 128 characters", App + "InvalidAcquirerLength");
    public static (string Message, string Code) ImportMerchantWithInvalidAcquirer => ("Invalid acquirer", App + "InvalidAcquirer");
    public static (string Message, string Code) ImportMerchantInvalidAcquirerCity => ("City is not matched with the provided acquirer", App + "ImportMerchantInvalidAcquirerCity");
    public static (string Message, string Code) ImportMerchantInvalidAcquirerGovernorate => ("Governorate is not matched with the provided acquirer", App + "ImportMerchantInvalidAcquirerGovernorate");
    public static (string Message, string Code) CityNotFromGovernorate => ("City is not matched with the provided governorate", App + "CityNotFromGovernorate");
    public static (string Message, string Code) ImportMerchantInvalidAcquirerBusinessDomain => ("Business domain is not matched with the provided acquirer", App + "ImportMerchantInvalidAcquirerBusinessDomain");
    public static (string Message, string Code) InvalidSubordinateMerchantTag => ("Field should be empty when the merchant tag is not a subordinate tag Allowed tags: Sub-Business, Sub-Wholesaler", App + "InvalidSubordinateMerchantTag");
    public static (string Message, string Code) ParentMerchantNotFound => ("Merchant not found for the given Member ID", App + "ParentMerchantNotFound");
    public static (string Message, string Code) ParentMerchantInvalidTag => ("Parent merchant has an invalid merchant tag Allowed tags: Master Business, Wholesaler", App + "ParentMerchantInvalidTag");
    public static (string Message, string Code) InvalidBusinessHierarchyTags => ("Subordinate business tag doesn't match the parent business tag", App + "InvalidBusinessHierarchyTags");
    public static (string Message, string Code) InvalidSoftposAquirer => ("Invalid softpos bank aquirer it must be NBE_BANK", App + "InvalidSoftposAquirer");
    public static (string Message, string Code) SoftposNotAvailableInEgypt => ("Softpos not available in egypt", App + "BundleIsInvalid");
    public static (string Message, string Code) InvalidEgyptPhoneNumberFormat => ("Invalid phone number format", App + "InvalidEgyptPhoneNumberFormat");


    public static (string Message, string Code) Invalid_ApplicationID => ("Invalid Application Id", App + "Invalid_ApplicationID");
    public static (string Message, string Code) ApplicationID_length => ("Application Id should not be more than 36 characters", App + "ApplicationID_length");
    public static (string Message, string Code) ApplicationID_Empty => ("Application Id should not be empty", App + "ApplicationID_Empty");
    public static (string Message, string Code) Invalid_RequestID => ("Invalid Request Id", App + "Invalid_RequestID");
    public static (string Message, string Code) RequestID_length => ("Request Id should not be more than 36 characters", App + "RequestID_length");
    public static (string Message, string Code) RequestID_Empty => ("Request Id should not be empty", App + "RequestID_Empty");
    public static (string Message, string Code) InvalidIntegrationID => ("Invalid Integration Id", App + "InvalidIntegrationID");
    public static (string Message, string Code) IntegrationID_Length => ("Integration Id should not be greater than 16 characters", App + "IntegrationID_Length");
    public static (string Message, string Code) CounterParty_length => ("Counter Party length should not be more than 12 Characters", App + "CounterParty_length");
    public static (string Message, string Code) Invalid_CounterParty => ("Invalid Counter Party", App + "Invalid_CounterParty");
    public static (string Message, string Code) CounterParty_Empty => ("Counter party should not be empty", App + "CounterParty_Empty");
    public static (string Message, string Code) InvalidUserID => ("UserID length should not be more than 36 Characters", App + "InvalidUserID");
    public static (string Message, string Code) UserID_Empty => ("UserID should not be empty", App + "UserID_Empty");
    public static (string Message, string Code) MCC_Empty => ("MCC should not be empty", App + "MCC_Empty");
    public static (string Message, string Code) MCC_Invalid => ("MCC in not valid or it was not found in reference data", App + "MCC_Invalid");
    public static (string Message, string Code) Business_LegalName_Empty => ("Legal Name should not be empty", App + "Business_LegalName_Empty");
    public static (string Message, string Code) Invalid_LegalName => ("Legal name length should not be greater than 64 characters", App + "Invalid_LegalName");
    public static (string Message, string Code) TradingName_Empty => ("Doing Business As Name should not be empty", App + "TradingName_Empty");
    public static (string Message, string Code) Invalid_TradingName => ("Invalid Trading name, it should not be greater than 64 characters", App + "Invalid_TradingName");
    public static (string Message, string Code) MunicipalLicenseNumber_Empty => ("Trading License Number should not be empty", App + "MunicipalLicenseNumber_Empty");
    public static (string Message, string Code) CommercialLegalType_Empty => ("Commercial legal type should not be empty", App + "CommercialLegalType_Empty");
    public static (string Message, string Code) CommercialLegalType_Length => ("Commercial legal type should have maximum 12 characters", App + "CommercialLegalTypeLength");
    public static (string Message, string Code) Invalid_CommercialLegalType => ("Commercial legal type is invalid or it was not found in reference data", App + "InvalidCommercialLegalType");
    public static (string Message, string Code) TradeLicenseIssueDate_Invalid => ("Trade License Issue date is invalid", App + "TradeLicenseIssueDate_Invalid");
    public static (string Message, string Code) TradeLicenseExpiryDate_Empty => ("Trade License Expiry date should not be empty", App + "TradeLicenseExpirtyDate_Empty");
    public static (string Message, string Code) TradeLicenseExpirtyDate_Invalid => ("Trade License Expiry date is invalid", App + "TradeLicenseExpirtyDate_Invalid");
    public static (string Message, string Code) LocationType_Invalid => ("Location Type value is invalid or it was not found in reference data", App + "LocationType_Invalid");
    public static (string Message, string Code) BankID_Empty => ("Bank ID should not be empty", App + "BankID_Empty");
    public static (string Message, string Code) BankID_length => ("Bank ID should not be greater than 16 characters", App + "BankID_length");
    public static (string Message, string Code) BankID_invalid => ("Bank ID is not valid", App + "BankID_invalid");
    public static (string Message, string Code) Dob_Empty => ("Date of birth should not be empty", App + "Dob_Empty");
    public static (string Message, string Code) Dob_invalid => ("Date of birth is not valid, should not be greater than or equal to current date", App + "Dob_invalid");
    public static (string Message, string Code) Iban_Max34 => ("IBAN must have a maximum of 34 characters", App + "Iban_Max34");
    public static (string Message, string Code) NationID_IssueDate_Empty => ("National ID issue date should not be empty", App + "NationID_IssueDate_Empty");
    public static (string Message, string Code) NationID_IssueDate_Invalid => ("National ID issue date is invalid", App + "NationID_IssueDate_Invalid");
    public static (string Message, string Code) NationalId_Length => ("National id should have 15 digits, and should start with 784", App + "NationalId_Length");

    public static (string Message, string Code) Action_Empty => ("Action should not be empty", App + "Action_Empty");
    public static (string Message, string Code) Action_Invalid => ("Action filed value is not valid and it's must be A or U", App + "Action_Invalid");
    public static (string Message, string Code) Eamil_Duplicate => ("Email ID should not be duplicate", App + "Eamil_Duplicate");
    public static (string Message, string Code) NationalId_Duplicate => ("Emirates ID should not be duplicate", App + "NationalId_Duplicate");
    public static (string Message, string Code) NationID_ExpiryDate_Invalid => ("National ID Expiry date is invalid", App + "NationID_ExpiryDate_Invalid");
    public static (string Message, string Code) NationID_ExpiryDate_Empty => ("National ID Expiry date should not be empty", App + "NationID_ExpiryDate_Empty");
    public static (string Message, string Code) Passport_Empty => ("Passport number should not be empty", App + "Passport_Empty");
    public static (string Message, string Code) Passport_Duplicate => ("Passport number should not be duplicate", App + "Passport_Duplicate");
    public static (string Message, string Code) Passport_length => ("Passport number should not be greater than 16 characters", App + "Passport_length");
    public static (string Message, string Code) Passport_IssueDate_Invalid => ("Passport issue date is invalid", App + "Passport_IssueDate_Invalid");
    public static (string Message, string Code) Passport_ExpiryDate_Invalid => ("Passport expiry date is invalid", App + "Passport_ExpiryDate_Invalid");
    public static (string Message, string Code) Passportissued_Empty => ("Passport issue date should not be empty", App + "Passportissued_Empty");
    public static (string Message, string Code) Passportexpire_Empty => ("Passport expiry date should not be empty", App + "Passportexpire_Empty");
    public static (string Message, string Code) PayOutCapamount_Empty => ("PayOut Cap amount should not be empty", App + "PayOutCapamount_Empty");
    public static (string Message, string Code) PayOutCapamount_Invalid => ("PayOut Cap amount is invalid", App + "PayOutCapamount_Invalid");
    public static (string Message, string Code) PayOutMinamount_Empty => ("PayOut Min amount should not be empty", App + "PayOutMinamount_Empty");
    public static (string Message, string Code) PayOutMinamount_Invalid => ("PayOut Min amount is invalid", App + "PayOutMinamount_Invalid");
    public static (string Message, string Code) SettlementTimeFrame_Invalid => ("Settlement TimeFrame value is invalid", App + "SettlementTimeFrame_Invalid");
    public static (string Message, string Code) PerTransactionLimit_Invalid => ("Per Transaction Limit value is invalid", App + "PerTransactionLimit_Invalid");
    public static (string Message, string Code) PerTransactionRefundLimit_Invalid => ("Per Transaction Refund Limit value is invalid", App + "PerTransactionRefundLimit_Invalid");
    public static (string Message, string Code) OrderSetupFeePaymentMode_Empty => ("Order Setup Fee Payment Mode should not be empty", App + "OrderSetupFeePaymentMode_Empty");
    public static (string Message, string Code) OrderSetupFeePaymentMode_Length => ("Order Setup Fee Payment Mode should not be greater than 4 charcters", App + "OrderSetupFeePaymentMode_Length");
    public static (string Message, string Code) OrderSetupFeePaymentMode_invalid => ("Order Setup Fee Payment Mode is invalid or it was not found in reference data", App + "OrderSetupFeePaymentMode_invalid");
    public static (string Message, string Code) OrderSetupFeePaymentReference_invalid => ("Order Setup Fee Payment Reference is invalid or it was not found in reference data", App + "OrderSetupFeePaymentReference_invalid");
    public static (string Message, string Code) PayoutSchedule_Empty => ("Payout schedule should not be empty", App + "PayoutSchedule_Empty");
    public static (string Message, string Code) PayoutSchedule_Length => ("Payout schedule should not be greater than 1 charcter", App + "PayoutSchedule_Length");
    public static (string Message, string Code) PayoutSchedule_invalid => ("Payout schedule is invalid or it was not found in reference data", App + "PayoutSchedule_invalid");
    public static (string Message, string Code) OrderSecurityDepositPaymentMode_Empty => ("Order Security Deposit Payment Mode should not be empty", App + "OrderSecurityDepositPaymentMode_Empty");
    public static (string Message, string Code) OrderSecurityDepositPaymentMode_invalid => ("Order Security Deposit Payment Mode is invalid or it was not found in reference data", App + "OrderSecurityDepositPaymentMode_invalid");
    public static (string Message, string Code) OrderSecurityDepositPaymentReference_Invalid => ("Order Security Deposit Payment Reference value invalid or it was not found in reference data", App + "OrderSecurityDepositPaymentReference_Invalid");
    public static (string Message, string Code) CommissionType_Empty => ("Commission Type should not be empty", App + "CommissionType_Empty");
    public static (string Message, string Code) CommissionType_Length => ("Commission Type length should not be greater than 20 charcters", App + "CommissionType_Length");
    public static (string Message, string Code) CommissionType_invalid => ("Commission Type is invalid or it was not found in reference data", App + "CommissionType_invalid");
    public static (string Message, string Code) ProductCode_Empty => ("Product Code length should not be empty", App + "ProductCode_Empty");
    public static (string Message, string Code) ProductCode_Length => ("Product Code should not be greater than 30 charcters", App + "ProductCode_Length");
    public static (string Message, string Code) ProductCode_invalid => ("Product Code is invalid ", App + "ProductCode_invalid");
    public static (string Message, string Code) CommissionValue_Empty => ("Commission Type should not be empty", App + "Commissioor it was not found in reference datanValue_Empty");
    public static (string Message, string Code) ProductBundle_Empty => ("Product Bundle should not be empty", App + "ProductBundle_Empty");
    public static (string Message, string Code) ProductBundle_Length => ("Product Bundle should not be greater than 30 characters", App + "ProductBundle_Length");
    public static (string Message, string Code) ProductBundle_Invalid => ("Product Bundle is invalid or it was not found in reference data", App + "ProductBundle_Invalid");
    public static (string Message, string Code) ProductQunatity_Empty => ("Product quantity should not be 0 or should not be empty", App + "ProductQunatity_Empty");
    public static (string Message, string Code) ProductQunatity_invalid => ("Product quantity should not be 0 or should not be greater than 100", App + "ProductQunatity_invalid");
    public static (string Message, string Code) WebsiteUrl_invalid => ("Invalid Web site URL", App + "WebsiteUrl_invalid");
    public static (string Message, string Code) WebsiteUrl_length => ("Website url length should not be more than 64 characters", App + "WebsiteUrl_length");

    public static (string Message, string Code) SettlementCurrency_Empty => ("Settlement Currency should not be empty", App + "SettlementCurrency_Empty");
    public static (string Message, string Code) SettlementCurrency_invalid => ("Settlement Currency value is invalid", App + "SettlementCurrency_invalid");
    public static (string Message, string Code) SettlementCurrency_Length => ("Settlement Currency should not be greater than 3 characters", App + "SettlementCurrency_Length");

    public static (string Message, string Code) TransactionCurrency_Empty => ("Transaction Currency should not be empty", App + "TransactionCurrency_Empty");
    public static (string Message, string Code) TransactionCurrency_invalid => ("Transaction Currency is invalid", App + "TransactionCurrency_invalid");
    public static (string Message, string Code) TransactionCurrency_Length => ("Transaction Currency should not be greater than 3 characters", App + "TransactionCurrency_Empty");
    public static (string Message, string Code) AnnualProjectedTotalAmount_Invalid => ("Annual projected total amount is invalid", App + "AnnualProjectedTotalAmount_InvalidType");
    public static (string Message, string Code) AnnualTurnOver_Invalid => ("Annual TurnOver is invalid", App + "AnnualTurnOver_Invalid");
    public static (string Message, string Code) YearsInBusinessInUAE_Length => ("Years in business in UAE length should not be more than 3 characters", App + "YearsInBusinessInUAE_Length");
    public static (string Message, string Code) YearInBusinessInUAE_Length => ("Year in business in UAE length should not be more than 3 characters", App + "YearInBusinessInUAE_Length");
    public static (string Message, string Code) SourceOfIncome_Length => ("Source Of Income length should not be more than 32 characters", App + "SourceOfIncome_Length");
    public static (string Message, string Code) SourceCountryOfIncome_Length => ("Source Country Of Income length should not be more than 32 characters", App + "SourceCountryOfIncome_Length");
    public static (string Message, string Code) SourceOfInitialCapital_Length => ("Source Of Initial Capital length should not be more than 32 characters", App + "SourceOfInitialCapital_Length");
    public static (string Message, string Code) KeySupplierCompany_Length => ("Key Supplier Company length should not be more than 32 characters", App + "KeySupplierCompany_Length");
    public static (string Message, string Code) KeySupplierCountry_Length => ("Key Supplier Country length should not be more than 32 characters", App + "KeySupplierCountry_Length");
    public static (string Message, string Code) KeyCustomerCompany_Length => ("Key Customer Company length should not be more than 32 characters", App + "KeyCustomerCompany_Length");
    public static (string Message, string Code) RelationshipWithOtherAcquirerName_Length => ("Relationship with other Acquirer aame length should not be more than 32 characters", App + "RelationshipWithOtherAcquirerName_Length");
    public static (string Message, string Code) AverageSingleTransaction_Invalid => ("Invalid Average single transaction", App + "AverageSingleTransaction_Invalid");
    public static (string Message, string Code) NumberOfEmployees_Invalid => ("Invalid Number of Employees", App + "NumberOfEmployees_Invalid");
    public static (string Message, string Code) NumberOfBranches_Invalid => ("Invalid Number of Branches", App + "NumberOfBranches_Invalid");
    public static (string Message, string Code) Mid_Length => ("MID must be 15 characters", App + "Mid_length");
    public static (string Message, string Code) Mid_Invalid => ("MID value is invalid, MID should be 15 digit", App + "Mid_Invalid");
    public static (string Message, string Code) OrderSecurityDepositPaymentReference_length => ("Order security deposit payment reference length should not be more than 12 characters", App + "OrderSecurityDepositPaymentReference_length");
    public static (string Message, string Code) OrderSetupFeePaymentReference_Length => ("Order setup fee payment reference length should not be more than 12 characters", App + "OrderSetupFeePaymentReference_Length");
    public static (string Message, string Code) DCCProvider_Length => ("DCC Provider length should not be more than 60 characters", App + "DCCProvider_Length");
    public static (string Message, string Code) PayOutDay_Invalid => ("PayOut Day value should not be 0 or greater than 27", App + "PayOutDay_Invalid");
    public static (string Message, string Code) PayOutDay_Empty => ("PayOut Day should not be empty", App + "PayOutDay_Empty");
    public static (string Message, string Code) AcceptedPaymentMethods_Empty => ("Accepted payment methods should not be empty", App + "AcceptedPaymentMethods_Empty");
    public static (string Message, string Code) AcceptedPaymentMethods_Invalid => ("Accepted payment is invalid or it was not found in reference data", App + "AcceptedPaymentMethods_Invalid");
    public static (string Message, string Code) AcceptedPaymentMethods_Length => ("Accepted payment methods length should not be more than 3 characters", App + "AcceptedPaymentMethods_Length");
    public static (string Message, string Code) TransactionType_Empty => ("Transaction Type should not be empty", App + "TransactionType_Empty");
    public static (string Message, string Code) TransactionType_Invalid => ("Transaction type is invalid or it was not found in reference data", App + "TransactionType_Invalid");
    public static (string Message, string Code) TransactionType_Length => ("Transaction type length should not be more than 20 characters", App + "TransactionType_Length");
    public static (string Message, string Code) Zip_Length => ("Zip code length should not be more than 12 characters", App + "Zip_Length");
    public static (string Message, string Code) Addresspin_Length => ("Address pin length should not be more than 32 characters", App + "Addresspin_Length");
    public static (string Message, string Code) AddressPin_Length => ("Address pin length should not be more than 100 characters", App + "AddressPin_Length");
    public static (string Message, string Code) Designation_length => ("Designation length should not be more than 16 characters", App + "Designation_length");
    public static (string Message, string Code) Relation_length => ("Relation length should not be more than 16 characters", App + "Relation_length");
    public static (string Message, string Code) LocationType_Length => ("Location Type length should not be more than 25 characters", App + "LocationType_Length");
    public static (string Message, string Code) VatNumber_length => ("VatNumber length should not be more than 64 characters", App + "VatNumber_length");
    public static (string Message, string Code) VatNumber_Invalid => ("VatNumber Invalid, It is must be a combination of alphanumeric", App + "VatNumber_Invalid");
    public static (string Message, string Code) TradeLicenseAuthority_length => ("Trade License Authority length should not be more than 64 characters", App + "TradeLicenseAuthority_length");
    public static (string Message, string Code) Tag_length => ("Tag length should not be more than 12 characters", App + "Tag_length");
    public static (string Message, string Code) SalesId_length => ("Sales id length should not be more than 10 characters", App + "SalesId_length");
    public static (string Message, string Code) SalesPartnerId_length => ("Sales Partner Id length should not be more than 10 characters", App + "SalesPartnerId_length");
    public static (string Message, string Code) BusinessId_length => ("Business Id length should not be more than 36 characters", App + "BusinessId_length");
    public static (string Message, string Code) ChainId_length => ("Chain Id length should not be more than 11 characters", App + "ChainId_length");
    public static (string Message, string Code) ChainName_length => ("Chain Name length should not be more than 64 characters", App + "ChainName_length");
    public static (string Message, string Code) EitherNationalIdOrPassportNumber_Required => ("NationalId and PassportNumber both cannot be empty", App + "EitherNationalIdOrPassportNumber_Required");
    public static (string Message, string Code) NationalId_Required_IfNationality_UAE => ("NationalId cannot be empty if the nationality of the shareholder is UAE", App + "NationalId_Required_IfNationality_UAE");
    public static (string Message, string Code) InvalidCP => ("CP should have GEIDEA_POS bundle and Quantity should not be 0 and not greater than 100", App + "InvalidCP");
    public static (string Message, string Code) InvalidCNP => ("CNP should have PAY_BY_LINK_BUNDLE or PAYMENT_GATEWAY_BUNDLE and Quantity should be 1 only", App + "InvalidCNP");
    public static (string Message, string Code) MerchantId_Empty => ("Merchant Id should not be empty", App + "MerchantId_Empty");
    public static (string Message, string Code) ShareholderId_Empty => ("Shareholder Id should not be empty", App + "ShareholderId_Empty");
    public static (string Message, string Code) OwnershipPercentage_length => ("Ownership percentage must be between 0 and 100", App + "OwnershipPercentage_length");
    public static (string Message, string Code) ShareholderCompany_empty => ("Shareholder Company Should not be empty", App + "ShareholderCompany_empty");
    public static (string Message, string Code) WathqRelationId_length => ("WathqRelation Id must be between 1 and 10000", App + "WathqRelationId_length");
    public static (string Message, string Code) CompanyName_Empty => ("Company Name should not be empty", App + "CompanyName_Empty");
    public static (string Message, string Code) CompanyName_length => ("Company Name Should not be more than 64 characters", App + "CompanyName_length");
    public static (string Message, string Code) PhonePrefix_Empty => ("Phone Prefix should not be empty", App + "PhonePrefix_Empty");
    public static (string Message, string Code) WrongPhonePrefix => ("Phone prefix does not belong to the country", App + "WrongPhonePrefix");
    public static (string Message, string Code) PhonePrefixMaxLength => ("Phone prefix should not more than 16 charcters", App + "PhonePrefixMaxLength");
    public static (string Message, string Code) CompanyType_empty => ("Company Type should not be empty", App + "CompanyType_empty");
    public static (string Message, string Code) CompanyType_length => ("Company Type Should not be more than 70 characters", App + "CompanyType_length");
    public static (string Message, string Code) Country_empty => ("Country should not be empty", App + "Country_empty");
    public static (string Message, string Code) CountryLength => ("Country length should not be more than 3 characters", App + "CountryLength");
    public static (string Message, string Code) LicenceIssueDate_empty => ("Licence Issue Date should not be empty", App + "LicenceIssueDate_empty");
    public static (string Message, string Code) LicenceIssueDate_invalid => ("Licence Issue Date is invalid", App + "LicenceIssueDate_invalid");
    public static (string Message, string Code) LicenseExpiryDate_empty => ("Licence Expiry Date should not be empty", App + "LicenseExpiryDate_empty");
    public static (string Message, string Code) LicenceExpiryDate_invalid => ("Licence Expiry Date is invalid", App + "LicenceExpiryDate_invalid");
    public static (string Message, string Code) CompanyLicence_empty => ("Company License should not be empty", App + "CompanyLicence_empty");
    public static (string Message, string Code) CompanyLicence_length => ("Company License should not be more than 64 charcters", App + "CompanyLicence_length");
    public static (string Message, string Code) CompanyType_invalid => ("Company Type is invalid", App + "CompanyType_invalid");

}