﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common.Models.Apex
{
    public static class ApexRequestTransformer
    {
        public static MerchantAction GetMerchant(Common.Models.Merchant merchant, MerchantAction merchantApex)
        {
            // if level is 1  - MerchantId ==> ChainId
            // if level is 2  - MerchantId ==> BusinessId && ParentMerchantId ==> ParentBusinessId [optional]
            if (merchantApex.Merchant.MerchantLevel == OrganizationLevel.Business)
            {
                merchantApex.Merchant.MerchantId = merchant.MerchantDetails.BusinessId;
                merchantApex.Merchant.ParentMerchantId = null;
            }
            // if level is 3 - MerchantId ==> MID && ParentMerchantId ==> BusinessId
            if (merchantApex.Merchant.MerchantLevel == OrganizationLevel.Account)
            {
                merchantApex.Merchant.MerchantId = merchant.MerchantDetails.MID;
                merchantApex.Merchant.ParentMerchantId = merchant.MerchantDetails.BusinessId;
            }

            return merchantApex;
        }

        public static class OrganizationLevel
        {
            public const string Chain = "1";
            public const string Business = "2";
            public const string Account = "3";
        }
        public static class Dateformat
        {
            public const string DateFormatter = "yyyyMMdd";
        }
        public static class BicMapper
        {
            public static List<ApexBicMapping> CheckMapping()
            {
                List<ApexBicMapping> mappedList = new()
                {
                    new ApexBicMapping { BIC = "RIBLSARI", AcquiringLedger = "DEFAULT_BANK" },
                    new ApexBicMapping { BIC = "RJHISARI", AcquiringLedger = "ARB" },
                    new ApexBicMapping { BIC = "SABBSARI", AcquiringLedger = "SABB" },
                    new ApexBicMapping { BIC = "SABBSARI", AcquiringLedger = "SABB_BANK" }
                };
                return mappedList;
            }
        }

        public static class ProductMapper
        {

            public static string GetBankName(string bankName)
            {
                if (string.IsNullOrEmpty(bankName))
                {
                    return string.Empty;
                }

                List<ApexMapping> mappedList = new()
                {
                    new ApexMapping { MMS = "The Royal Bank Of Scotland N.V.", APEX = "ABN AMRO / ROYAL BANK OF SCOTLAND" },
                    new ApexMapping { MMS = "Abu Dhabi Commercial Bank - ADCB", APEX = "ABU DHABI COMMERCIAL BANK" },
                    new ApexMapping { MMS = "Al Ahli Bank Of Kuwait K.S.C.", APEX = "AL AHLI BANK OF KUWAIT" },
                    new ApexMapping { MMS = "Raf idain Bank", APEX = "Raf idain Bank" },
                    new ApexMapping { MMS = "Arab African International Bank", APEX = "ARAB AFRICAN INTERNATIONAL" },
                    new ApexMapping { MMS = "Al Masraf", APEX = "Al Masraf" },
                    new ApexMapping { MMS = "Arab Bank", APEX = "ARAB BANK" },
                    new ApexMapping { MMS = "Bank of Baroda", APEX = "BANK OF BARODA" },
                    new ApexMapping { MMS = "Bank of Sharjah", APEX = "BANK OF SHARJAH" },
                    new ApexMapping { MMS = "Blom Bank France", APEX = "Blom Bank France" },
                    new ApexMapping { MMS = "BANQUE MISR", APEX = "BANQUE DU CAIRE / BANK MISR" },
                    new ApexMapping { MMS = "Credit Agricole Corporate and Investment Bank", APEX = "CREDIT AGRICOLE" },
                    new ApexMapping { MMS = "Al Khaliji France S.A.", APEX = "Al Khaliji France S.A." },
                    new ApexMapping { MMS = "BNP Paribas", APEX = "BANQUE PARIBAS" },
                    new ApexMapping { MMS = "Barclays Bank", APEX = "BARCLAYS BANK" },
                    new ApexMapping { MMS = "HSBC Bank Middle East (for WPS Payments)", APEX = "HSBC BANK MIDDLE EAST" },
                    new ApexMapping { MMS = "HSBC Bank Middle East (for non-WPS Payments)", APEX = "HSBC BANK MIDDLE EAST" },
                    new ApexMapping { MMS = "Citibank NA", APEX = "Citibank NA" },
                    new ApexMapping { MMS = "Commercial Bank International PSC", APEX = "COMMERCIAL BANK INTERNATIONAL" },
                    new ApexMapping { MMS = "Commercial Bank of Dubai", APEX = "COMMERCIAL BANK OF DUBAI" },
                    new ApexMapping { MMS = "Dubai Islamic Bank", APEX = "DUBAI ISLAMIC BANK" },
                    new ApexMapping { MMS = "El Nilein Bank", APEX = "El Nilein Bank" },
                    new ApexMapping { MMS = "Emirates NBD Bank PJSC", APEX = "Emirates NBD Bank" },
                    new ApexMapping { MMS = "First Gulf Bank", APEX = "FIRST GULF BANK" },
                    new ApexMapping { MMS = "Habib Bank Limited", APEX = "HABIB BANK LTD." },
                    new ApexMapping { MMS = "Habib Bank AG Zurich", APEX = "HABIB BANK A.G. ZURICH" },
                    new ApexMapping { MMS = "Investbank PSC", APEX = "INVEST BANK" },
                    new ApexMapping { MMS = "Janata Bank", APEX = "JANATA BANK" },
                    new ApexMapping { MMS = "Mashreqbank", APEX = "MASHREQ BANK" },
                    new ApexMapping { MMS = "Emirates Islamic Bank PJSC", APEX = "EMIRATES ISLAMIC BANK" },
                    new ApexMapping { MMS = "First Abu Dhabi bank(FAB)", APEX = "First Abu Dhabi bank(FAB)" },
                    new ApexMapping { MMS = "National Bank Of Bahrain", APEX = "NATIONAL BANK OF BAHRAIN" },
                    new ApexMapping { MMS = "National Bank Of Fujairah", APEX = "NATIONAL BANK OF FUJAIRAH" },
                    new ApexMapping { MMS = "National Bank of Oman", APEX = "NATIONAL BANK OF OMAN" },
                    new ApexMapping { MMS = "National Bank of Ras Al-Khaimah", APEX = "NATIONAL BANK OF RAS AL KHAIMAH" },
                    new ApexMapping { MMS = "Sharjah Islamic Bank", APEX = "NATIONAL BANK OF SHARJAH / SHARJAH ISLAMIC BANK" },
                    new ApexMapping { MMS = "National Bank Of Umm Al Qaiwain", APEX = "National Bank Of Umm Al Qaiwain" },
                    new ApexMapping { MMS = "Industrial and Commercial Bank of China", APEX = "Industrial and Commercial Bank of China" },
                    new ApexMapping { MMS = "Standard Chartered Bank", APEX = "STANDARD CHARTERED" },
                    new ApexMapping { MMS = "Union National Bank", APEX = "UNION NATIONAL BANK" },
                    new ApexMapping { MMS = "United Arab Bank", APEX = "UNITED ARAB BANK" },
                    new ApexMapping { MMS = "United Bank Ltd.", APEX = "UNITED BANK LTD." },
                    new ApexMapping { MMS = "Deutsche Bank", APEX = "DEUTSCHE BK" },
                    new ApexMapping { MMS = "Abu Dhabi Islamic Bank", APEX = "ABU DHABI ISLAMIC BANK" },
                    new ApexMapping { MMS = "Dubai Bank", APEX = "DUBAI BANK PJSC" },
                    new ApexMapping { MMS = "Noor Islamic Bank", APEX = "NOOR ISLAMIC BANK" },
                    new ApexMapping { MMS = "Al Hilal Bank", APEX = "AL HILAL BK" },
                    new ApexMapping { MMS = "Doha Bank", APEX = "DOHA BANK" },
                    new ApexMapping { MMS = "SAMBA Financial Group", APEX = "SAMBA" },
                    new ApexMapping { MMS = "National Bank Of Kuwait", APEX = "NATIONAL BANK OF KUWAIT" },
                    new ApexMapping { MMS = "Ajman Bank", APEX = "AJMAN BANK" },
                    new ApexMapping { MMS = "GULF INTERNATIONAL BANK", APEX = "GULF INTERNATIONAL BANK" },
                    new ApexMapping { MMS = "INTESA SANPAOLO", APEX = "INTESA SANPAOLO" },
                    new ApexMapping { MMS = "Al Maryah Community Bank LLC", APEX = "Al Maryah Community Bank LLC" },
                    new ApexMapping { MMS = "GPSSA – Pension Contributions and Disbursements", APEX = "GPSSA – Pension Contributions and Disbursements" },
                    new ApexMapping { MMS = "WIO BANK P.J.S.C.", APEX = "WIO BANK" }
                };
                string apexBankName = string.Empty;
                foreach (var product in mappedList)
                {
                    if (bankName.Equals(product.MMS))
                    {
                        apexBankName = product.APEX!;
                    }
                }
                return apexBankName;
            }

            public static List<string> GetApexProducts(string mmsProduct)
            {
                List<ApexMapping> mappedList = new()
                {
                   new ApexMapping { MMS = "JCB", APEX = "JC" },
                  new ApexMapping { MMS = "MRC", APEX = "ME" },
                  new ApexMapping { MMS = "DIN", APEX = "DI" },
                  new ApexMapping { MMS = "DIN", APEX = "DC" },
                  new ApexMapping { MMS = "VCS", APEX = "VL" },
                  new ApexMapping { MMS = "MCS", APEX = "ML" },
                  new ApexMapping { MMS = "AMX", APEX = "AX" },
                  new ApexMapping { MMS = "UPI", APEX = "UP" },
                  new ApexMapping { MMS = "RPY", APEX = "RU" },
                  new ApexMapping { MMS = "VCI", APEX = "VI" },
                  new ApexMapping { MMS = "MCI", APEX = "MI" },
                  new ApexMapping { MMS = "DBC", APEX = "LD" },
                  new ApexMapping { MMS = "MDA", APEX = "P1" },
                  new ApexMapping { MMS = "TMR", APEX = "TM" },
                  new ApexMapping { MMS = "TBY", APEX = "TB" },


                };

                var productsList = new List<string>();
                foreach (var product in mappedList)
                {
                    if (product.MMS!.Equals(mmsProduct, StringComparison.OrdinalIgnoreCase))
                    {
                        productsList.Add(product.APEX!);
                    }
                }

                return productsList ?? new List<string>();
            }

            public static List<string> GetApexProductsCommission(string[] mmsProduct)
            {
                List<ApexMapping> mappedList = new()
                {
                   new ApexMapping { MMS = "JCB", APEX = "JC" },
                  new ApexMapping { MMS = "MRC", APEX = "ME" },
                  new ApexMapping { MMS = "DIN", APEX = "DI" },
                  new ApexMapping { MMS = "DIN", APEX = "DC" },
                  new ApexMapping { MMS = "VCS", APEX = "VL" },
                  new ApexMapping { MMS = "MCS", APEX = "ML" },
                  new ApexMapping { MMS = "AMX", APEX = "AX" },
                  new ApexMapping { MMS = "UPI", APEX = "UP" },
                  new ApexMapping { MMS = "RPY", APEX = "RU" },
                  new ApexMapping { MMS = "VCI", APEX = "VI" },
                  new ApexMapping { MMS = "MCI", APEX = "MI" },
                  new ApexMapping { MMS = "DBC", APEX = "LD" },
                  new ApexMapping { MMS = "MDA", APEX = "P1" },
                  new ApexMapping { MMS = "TMR", APEX = "TM" },
                  new ApexMapping { MMS = "TBY", APEX = "TB" },
                };

                var productsList = new List<string>();
                foreach (var product in mmsProduct)
                {
                    foreach (var mapping in mappedList)
                    {
                        if (mapping!.MMS!.Equals(product, StringComparison.OrdinalIgnoreCase))
                        {
                            productsList.Add(mapping!.APEX!);
                        }
                    }
                }


                return productsList ?? new List<string>();
            }

            public static List<string> GetApexProductcodes(string[] mmsProduct)
            {
                List<ApexProductMapping> mappedList = new()
                {
                  new ApexProductMapping { MMS = "JCB", APEX = new List<string> { "JC" } },
                  new ApexProductMapping { MMS = "MRC", APEX = new List<string> { "ME" } },
                  new ApexProductMapping { MMS = "DIN", APEX = new List<string> { "DI", "DC" } },
                  new ApexProductMapping { MMS = "VSC", APEX = new List<string> { "VI", "VL" } },
                  new ApexProductMapping { MMS = "MSC", APEX = new List<string> { "MI", "ML" } },
                  new ApexProductMapping { MMS = "AMX", APEX = new List<string> { "AX" } },
                  new ApexProductMapping { MMS = "UPI", APEX = new List<string> { "UP" } },
                  new ApexProductMapping { MMS = "RPY", APEX = new List<string> { "RU" } },
                  new ApexProductMapping { MMS = "MDA", APEX = new List<string> { "P1" } },
                  new ApexProductMapping { MMS = "DBC", APEX = new List<string> { "LD" } },

                };

                var productsList = new List<string>();
                foreach (var productCode in mmsProduct)
                {
                    var apexProduct = mappedList
                        .Where(x => string.Equals(x.MMS, productCode, StringComparison.OrdinalIgnoreCase))
                        .SelectMany(x => x.APEX!)
                        .ToList();

                    if (apexProduct != null)
                    {
                        productsList.AddRange(apexProduct);
                    }
                }

                return productsList;
            }
        }
    }
}