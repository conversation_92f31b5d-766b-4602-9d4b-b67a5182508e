﻿using Common.Constants;
using Common.Services;
using FluentAssertions;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Channels;
using static Common.Constants.AppConstants;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Configuration;
using Services.Providers.Apex;

namespace Services.Tests
{
    public class MerchantClientTest
    {
        private readonly MerchantExchangeClient messageClient;
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private readonly IMerchantCreate msg = Substitute.For<IMerchantCreate>();
        private readonly IMerchantExchangeClient exchange = Substitute.For<IMerchantExchangeClient>();
        private readonly ILogger<MessageClient> logger = Substitute.For<ILogger<MessageClient>>();
        private IProviderService _providerService = Substitute.For<IProviderService>();
        private IApexService _apexService;
        public MerchantClientTest()
        {
            var options = Substitute.For<IOptionsMonitor<RabbitMqOptions>>();
            _apexService = Substitute.For<IApexService>();
            messageClient = new MerchantExchangeClient(contextAccessor, logger, options, msg);
        }

        [Test]
        public async Task MerchantMessageCreator_ShouldNotCreateMerchant_WhenEntityIsMerchant()
        {
            // Arrange
            var merchantMessage = new GenericMessage<Merchant>();
            var providers = new List<ProviderQueueConfigResponseModel>
        {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, Action = "Create", Entity = "Merchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, Action = "Create", Entity = "Chain" }

        };

            _providerService.GetProviderQueueConfiguration(Arg.Any<QueueConfigSearchFilter>())
           .Returns(Task.FromResult(providers));

            // Act
            await msg.MerchantMessageCreator(merchantMessage);

            // Assert
            await _apexService.Received(0).CreateMerchant(merchantMessage, providers);
        }


        [Test]
        public void OnMessageReceiving_WhenMerchantCreateTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Common.Models.Merchant>
            {
                Data = new Common.Models.Merchant()
                {
                    MerchantId = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    MerchantType = "Retail",
                    MerchantStatus = "Active",
                    Tag = "High-Value",
                    Counterparty = "GEIDEA_UAE",
                    ApplicationId = "App123"
                }
            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Merchant.Create"
            };
            bool merchantCreatedTrigger = false;
            messageClient.OnMessageQueueCreate += (sender, args) =>
            {
                merchantCreatedTrigger = true;
                args.MerchantMessageData.ApplicationId.Should().Be(merchantCreate.Data.ApplicationId);
                args.MerchantMessageData.Tag.Should().Be(merchantCreate.Data.Tag);
                args.MerchantMessageData.Counterparty.Should().Be(merchantCreate.Data.Counterparty);
                args.MerchantMessageData.MerchantStatus.Should().Be(merchantCreate.Data.MerchantStatus);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            merchantCreatedTrigger.Should().BeTrue();
        }

        [Test]
        public void OnMessageReceiving_WhenMerchantUpdateTriggered_ShouldTriggerEvent()
        {
            var merchantUpdate = new GenericMessage<Common.Models.Merchant>
            {
                Data = new Common.Models.Merchant()
                {
                    MerchantId = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    MerchantType = "Retail",
                    MerchantStatus = "Active",
                    Tag = "High-Value",
                    Counterparty = "Some Counterparty Inc.",
                    ApplicationId = "App123"
                }
            };
            //var eventArgs = new BasicDeliverEventArgs
            //{
            //    Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantUpdate)),
            //    RoutingKey = "NexusBridge.Merchant.Update",
            //    DeliveryTag = 1
            //};
            bool merchantUpdateTrigger = true;
            messageClient.OnMessageQueueUpdate += (sender, args) =>
            {
                args.MerchantMessageData.ApplicationId.Should().Be(merchantUpdate.Data.ApplicationId);
                args.MerchantMessageData.Tag.Should().Be(merchantUpdate.Data.Tag);
                args.MerchantMessageData.Counterparty.Should().Be(merchantUpdate.Data.Counterparty);
                args.MerchantMessageData.MerchantStatus.Should().Be(merchantUpdate.Data.MerchantStatus);
            };

            // Act
            //   messageClient.OnMessageReciving(null, eventArgs);

            // Assert
            merchantUpdateTrigger.Should().BeTrue();
        }

        [Test]
        public void OnMessageReceiving_WhenMerchantUpdateTriggered_Should_Fail()
        {
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes("{\"Id\": 1, \"Name\": \"Test Merchant\"}}"),
                RoutingKey = "NexusBridge.Merchant.Create"
            };

            messageClient.OnMessageReciving(null, eventArgs);
            logger.Received(1);
        }

        [Test]
        public void OnMessageReceiving_WhenMerchantUpdateStatusTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Common.Models.Merchant>
            {
                Data = new Common.Models.Merchant()
                {
                    MerchantId = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    MerchantType = "Retail",
                    MerchantStatus = "Active",
                    Tag = "High-Value",
                    Counterparty = "Some Counterparty Inc.",
                    ApplicationId = "App123"
                }
            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Merchant.UpdateStatus"
            };
            bool merchantCreatedTrigger = false;
            messageClient.OnMessageQueueUpdateStatus += (sender, args) =>
            {
                merchantCreatedTrigger = true;
                args.MerchantMessageData.ApplicationId.Should().Be(merchantCreate.Data.ApplicationId);
                args.MerchantMessageData.Tag.Should().Be(merchantCreate.Data.Tag);
                args.MerchantMessageData.Counterparty.Should().Be(merchantCreate.Data.Counterparty);
                args.MerchantMessageData.MerchantStatus.Should().Be(merchantCreate.Data.MerchantStatus);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            merchantCreatedTrigger.Should().BeTrue();
        }
    }
}