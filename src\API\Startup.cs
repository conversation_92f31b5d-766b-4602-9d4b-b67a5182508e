using API.Configuration.Extensions;
using Common.Models.Apex;
using Common.Options;
using Common.Services;
using Elastic.Apm.NetCoreAll;
using Geidea.PaymentGateway.ConfigServiceClient;
using Geidea.Utils.HealthChecks;
using Geidea.Utils.Versioning;
using Geidea.Utils.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.Filters;
using Services;
using Services.Orchestration;
using Services.Providers.Apex;
using API.Filters;
using System.IO;
using System.Reflection;
using System;
using Microsoft.Extensions.Hosting;
using System.Configuration;
using Services.Providers;
using Services.Helper.Interface;
using Services.Helper;
using Geidea.Utils.Logging;
using Messaging.Publishers;

namespace API;

public class Startup
{
    private readonly IConfiguration _configuration;

    public Startup(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.RegisterConfigurations(_configuration)
                .RegisterDependencies(_configuration);
        services.Configure<TokenServiceConfig>(_configuration.GetSection("TokenService"));
        services.AddTransient<IProviderService, ProviderService>();
        services.AddTransient<IRequestLogService, RequestLogService>();
        services.AddTransient<IMerchantCreate, MerchantCreate>();
        services.AddTransient<IOrderCreate, OrderCreate>();
        services.AddTransient<IProductCreate, ProductCreate>();

        services.AddTransient<IApexTerminalClient, ApexTerminalClient>();
        services.AddTransient<IApexToken, ApexToken>();
        services.AddTransient<IApexGetMerchantClient, ApexGetMerchantClient>();
        services.AddTransient<IApexBusinessAccountClient, ApexBusinessAccountClient>();
        services.AddTransient<IApexService, ApexService>();
        services.AddTransient<IHttpClientWrapper, HttpClientWrapper>();
        services.AddTransient<IRequestHeaders, RequestHeaders>();
        services.AddTransient<IHttpRequestExecutorService, HttpRequestExecutorService>();
        services.AddSingleton<IApexResponsePublisherClient, ApexResponsePublisherClient>();
        services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddHttpClient().AddHeaderPropagation();
        services.AddAutoMapper(typeof(Startup));
        services.AddTransient<CorrelationIdHandler>();
        services.AddMvc();
        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Version = "v1",
                Title = "Geidea NexusBridge Adapter v1",
                Description = "Geidea NexusBridge Adapter v1"
            });
            options.ExampleFilters();
            options.EnableAnnotations();
            options.IgnoreObsoleteActions();
            options.OperationFilter<CounterpartyHeaderFilter>();
            options.DocumentFilter<CommonSwaggerDocument>();

            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            options.IncludeXmlComments(xmlPath);
        });
        services.AddSwaggerExamplesFromAssemblyOf<Startup>();
        services.AddOptions<UrlSettings>().Bind(_configuration.GetSection("UrlSettings"))
           .Validate(o =>
           {
               return true;
           });
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        app.UseAllElasticApm(_configuration);

        app.UseHeaderPropagation();

        app.UseHealthChecks("/health", new HealthCheckOptions { AllowCachingResponses = false, ResponseWriter = HealthResponseWriter.WriteHealthCheckResponse });

        app.UseInternalConfigurationView();
        app.UseVersionMiddleware();

        app.StartMessageConsumption();

        if (!env.IsProduction())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Geidea NexusBridge Adapter v1");
                c.RoutePrefix = string.Empty;
            });
        }
        app.UseRouting();

    }
}