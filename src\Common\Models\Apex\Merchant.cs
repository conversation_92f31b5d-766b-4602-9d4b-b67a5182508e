﻿using System;
using System.Collections.Generic;

namespace Common.Models.Apex;

public class Merchant : MerchantAdditionalInfo
{
    public string? MerchantId { get; set; }
    public string? MerchantLevel { get; set; }
    public string? ParentMerchantId { get; set; }
    public string? StoreName { get; set; }
    public string? AlternativeStoreName { get; set; }
    public string? CommercialRegistrationNumber { get; set; }
    public string? Mcc { get; set; }
    public string? Status { get; set; }
    public Contact Contact { get; set; } = null!;
    public Address Address { get; set; } = null!;

    /// <summary>
    /// Newly added properties
    /// </summary>
    /// 
    public string? MerchantName { get; set; }
    public string? AlternativeMerchantName { get; set; }
    public string? MerchantSegment { get; set; }
    public string? RiskLevel { get; set; }
    public string? ContractStartDate { get; set; }
    public string? ContractExpiryDate { get; set; }
    public string? TradeLicenseIssueDate { get; set; } = string.Empty;
    public string? TradeLicenseExpiryDate { get; set; } = string.Empty;

    public string? IsHsbcMerchant { get; set; } = "N";
    public string? IsVip { get; set; } = "N";

    /// <summary>
    /// Bank details 
    /// </summary>
    /// 

    public string Iban { get; set; } = string.Empty;
    public string AccountNumber { get; set; } = string.Empty;
    public string BeneficiaryFullName { get; set; } = string.Empty;
    public string BIC { get; set; } = string.Empty;
    public string PayoutBeneficiaryBank { get; set; } = string.Empty;
    public string PayoutSourceBank { get; set; } = string.Empty;
    public string PayoutSourceBankType { get; set; } = string.Empty;


    public string PayoutSchedule { get; set; } = string.Empty;
    public int? PayoutOffset { get; set; }
    public string PayoutCapAmount { get; set; } = string.Empty;
    public string PayoutMinimumCap { get; set; } = string.Empty;
    public string PayoutTransferFeeAmount { get; set; } = string.Empty;
    public int ExpectedMonthlyVolume { get; set; }
    public int ExpectedHighestTransactionAmount { get; set; }
    public string VatNumber { get; set; } = string.Empty;
    public string SalesAgent { get; set; } = string.Empty;
    public string SalesManager { get; set; } = string.Empty;
    public string ClientNumber { get; set; } = string.Empty;

    public string UnifiedNumber { get; set; } = string.Empty;

    public string PaymentMethod { get; set; } = string.Empty;
    public int? TerminalOwnerId { get; set; }
    public string? BusinessType { get; set; } = string.Empty;

    public string SettlementCurrency { get; set; } = "000";

    public IReadOnlyCollection<string> Products { get; set; } = new List<string>();
    public IReadOnlyCollection<Commission> Commission { get; set; } = new List<Commission>();
    public IReadOnlyCollection<MerchantTerminal> LinkedTerminals { get; set; } = new List<MerchantTerminal>();
    public IReadOnlyCollection<MerchantFee> Fee { get; set; } = new List<MerchantFee>();
    public IReadOnlyCollection<MerchantDccSet> DccSet { get; set; } = new List<MerchantDccSet>();


}