﻿using NUnit.Framework;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Microsoft.Extensions.Options;
using Messaging;
using Geidea.Utils.Messaging.Base;
using Common.Services;
using Geidea.Utils.Messaging;
using Common.Models.Apex;
using System;
using Newtonsoft.Json;
using RabbitMQ.Client.Events;
using System.Text;
using FluentAssertions;
using Common.Models.Orders;

namespace Services.Tests
{
    public class OrderClientTest
    {
        private OrderExchangeClient messageClient;
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private readonly ILogger<MessageClient> logger = Substitute.For<ILogger<MessageClient>>();
        private readonly IOrderCreate orderMessage = Substitute.For<IOrderCreate>();
        public OrderClientTest()
        {
            var options = Substitute.For<IOptionsMonitor<RabbitMqOptions>>();

            messageClient = new OrderExchangeClient(contextAccessor, logger, options, orderMessage);
        }

        [Test]
        public void OnMessageReceiving_WhenOrderCreateTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Order>
            {
                Data = new Order()
                {


                    AgreementId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    StoreId = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    OrderStatus = "NEW",
                    TrackingNumber = "*********",
                    TrackingUrl = "https://tracking-url.com",
                    Shipper = "Some Shipper",
                    ShippedDate = DateTime.UtcNow,
                    CouponCode = "SUMMER10",
                    PaymentMethod = "Credit Card",
                    CompanyRegNo = "ABC123",
                    SalesName = "John Doe",
                    SubscriptionPlan = "Premium",
                    Note = "This is a test order",
                    MerchantName = "MerchantABC",
                    Subtotal = 100,
                    Discount = 10,
                    VatPercent = 20,
                    Vat = 18,
                    Total = 108,
                    MonthlySubtotal = 80,
                    MonthlyDiscount = 8,
                    MonthlyVat = 16,
                    MonthlyTotal = 88,
                    YearlySubtotal = 960,
                    YearlyDiscount = 96,
                    YearlyVat = 192,
                    YearlyTotal = 1056,
                    Currency = "USD",
                    DeliveryMethod = "Express",
                    DeliveryDays = 3,
                    ProofOfDelivery = true,
                    BillPaymentsStatus = "Paid",
                    MigrationRequest = false
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Order.Create"
            };
            bool orderCreatedTrigger = false;
            messageClient.OnMessageQueueOrderCreate += (sender, args) =>
            {
                orderCreatedTrigger = true;
                args.OrderMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            orderCreatedTrigger.Should().BeTrue();
        }

        public void OnMessageReceiving_WhenOrderUpdateTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Order>
            {
                Data = new Order()
                {


                    AgreementId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    StoreId = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    OrderStatus = "NEW",
                    TrackingNumber = "*********",
                    TrackingUrl = "https://tracking-url.com",
                    Shipper = "Some Shipper",
                    ShippedDate = DateTime.UtcNow,
                    CouponCode = "SUMMER10",
                    PaymentMethod = "Credit Card",
                    CompanyRegNo = "ABC123",
                    SalesName = "John Doe",
                    SubscriptionPlan = "Premium",
                    Note = "This is a test order",
                    MerchantName = "MerchantABC",
                    Subtotal = 100,
                    Discount = 10,
                    VatPercent = 20,
                    Vat = 18,
                    Total = 108,
                    MonthlySubtotal = 80,
                    MonthlyDiscount = 8,
                    MonthlyVat = 16,
                    MonthlyTotal = 88,
                    YearlySubtotal = 960,
                    YearlyDiscount = 96,
                    YearlyVat = 192,
                    YearlyTotal = 1056,
                    Currency = "USD",
                    DeliveryMethod = "Express",
                    DeliveryDays = 3,
                    ProofOfDelivery = true,
                    BillPaymentsStatus = "Paid",
                    MigrationRequest = false
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Order.Update"
            };
            bool orderUpdateTrigger = false;
            messageClient.OnMessageQueueOrderUpdate += (sender, args) =>
            {
                orderUpdateTrigger = true;
                args.OrderMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            orderUpdateTrigger.Should().BeTrue();
        }

        public void OnMessageReceiving_WhenOrderUpdateStatusTriggered_ShouldTriggerEvent()
        {
            var merchantCreate = new GenericMessage<Order>
            {
                Data = new Order()
                {


                    AgreementId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    StoreId = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    OrderStatus = "NEW",
                    TrackingNumber = "*********",
                    TrackingUrl = "https://tracking-url.com",
                    Shipper = "Some Shipper",
                    ShippedDate = DateTime.UtcNow,
                    CouponCode = "SUMMER10",
                    PaymentMethod = "Credit Card",
                    CompanyRegNo = "ABC123",
                    SalesName = "John Doe",
                    SubscriptionPlan = "Premium",
                    Note = "This is a test order",
                    MerchantName = "MerchantABC",
                    Subtotal = 100,
                    Discount = 10,
                    VatPercent = 20,
                    Vat = 18,
                    Total = 108,
                    MonthlySubtotal = 80,
                    MonthlyDiscount = 8,
                    MonthlyVat = 16,
                    MonthlyTotal = 88,
                    YearlySubtotal = 960,
                    YearlyDiscount = 96,
                    YearlyVat = 192,
                    YearlyTotal = 1056,
                    Currency = "USD",
                    DeliveryMethod = "Express",
                    DeliveryDays = 3,
                    ProofOfDelivery = true,
                    BillPaymentsStatus = "Paid",
                    MigrationRequest = false
                }

            };
            var eventArgs = new BasicDeliverEventArgs
            {
                Body = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantCreate)),
                RoutingKey = "NexusBridge.Order.UpdateStatus"
            };
            bool orderUpdateTrigger = false;
            messageClient.OnMessageQueueOrderStatus += (sender, args) =>
            {
                orderUpdateTrigger = true;
                args.OrderMessageData.MerchantId.Should().Be(merchantCreate.Data.MerchantId);
            };

            messageClient.OnMessageReciving(null, eventArgs);
            orderUpdateTrigger.Should().BeTrue();
        }
    }
}
