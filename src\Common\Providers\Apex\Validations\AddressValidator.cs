﻿using Common.Constants;
using Common.Exceptions;
using Common.Models;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class AddressValidator : AbstractValidator<Address>
{
    public AddressValidator(string counterparty)
    {
        if(counterparty == AppConstants.CounterParty.UAE)
        {
           RuleFor(x => x.Governorate)
          .NotEmpty()
          .WithErrorCode(Errors.GovernorateRequired.Code)
          .WithMessage(Errors.GovernorateRequired.Message);
        }

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithErrorCode(Errors.ValidEmailRequired.Code)
            .WithMessage(Errors.ValidEmailRequired.Message);

        RuleFor(e => e.Email)
            .Matches(@"^(([^<>()\[\]\\.,;:\s@""]+(\.[^<>()\[\]\\.,;:\s@""]+)*)|("".+""))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$")
            .WithErrorCode(Errors.User_Invalid_Email_Address.Code)
            .WithMessage(Errors.User_Invalid_Email_Address.Message)
            .MaximumLength(60)
            .WithErrorCode(Errors.EmailMaxLength.Code)
            .WithMessage(Errors.EmailMaxLength.Message)
            .When(m => !string.IsNullOrEmpty(m.Email));

    }
}