﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net6.0</TargetFramework>
	  <LangVersion>latest</LangVersion>
    <IsPackable>false</IsPackable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ProjectGuid>{D2BDFABE-4784-462A-9010-B11754BE6513}</ProjectGuid>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.msbuild" Version="3.2.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Geidea.Messages" Version="2020.8.3.3" />
    <PackageReference Include="Geidea.Messaging" Version="2.2.256" />
    <PackageReference Include="Geidea.Utils.Json" Version="1.1.251" />
    <PackageReference Include="Geidea.Utils.Messaging" Version="1.0.32" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
    <PackageReference Include="NSubstitute" Version="4.4.0" />
    <PackageReference Include="nunit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.3.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Messaging\Messaging.csproj" />
    <ProjectReference Include="..\Services\Services.csproj" />
  </ItemGroup>

</Project>
