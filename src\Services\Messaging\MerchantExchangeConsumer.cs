﻿using Common.Exceptions;
using Common.Models.Messaging;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;

namespace Services.Messaging;

public class MerchantExchangeConsumer
{
    private readonly ILogger<MerchantExchangeConsumer> _logger;
    private readonly IMerchantExchangeClient _merchantClient;

    public MerchantExchangeConsumer(ILogger<MerchantExchangeConsumer> logger,
        IMerchantExchangeClient merchantClient)
    {
        _logger = logger;
        _merchantClient = merchantClient;
    }

    public void BeginReceive()
    {
        _merchantClient.OnMessageQueueCreate += MessageClient_OnMessageReceived;
        _merchantClient.OnMessageQueueUpdate += MessageClient_OnMessageReceived;
        _merchantClient.OnMessageQueueUpdateStatus += MessageClient_OnMessageReceived;
        _merchantClient.Connect();
    }

    public void MessageClient_OnMessageReceived(object? sender, MerchantMessageBody e)
    {
        try
        {
            //Task.Run(() => _merchantService.CreateMerchantMessageServiceAsync(e.MerchantMessageData!)).Wait();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unknown exception thrown when trying to update adapter");
            throw new ServiceException(Errors.UnknownException);
        }
    }
}