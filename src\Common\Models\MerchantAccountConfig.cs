﻿using System;

namespace Common.Models;

public class MerchantAccountConfig
{
    public Guid Id { get; set; }
    public Guid MerchantId { get; set; }
    public string? SettlementCurrency { get; set; }
    public string? PayoutSchedule { get; set; }
    public int PayoutDay { get; set; }
    public decimal PayoutCapAmount { get; set; }
    public decimal PayoutMinAmount { get; set; }
    public string? PayoutBank { get; set; }
    public int PayoutMinimumCap { get; set; }
    public int PayoutTransferFeeAmount { get; set; }
    public string? Bic { get; set; }
    public string? TradeLicenseIssueDate { get; set; }
    public string? TradeLicenseExpiryDate { get; set; }
    public int ExpectedMonthlyVolume { get; set; }
    public int ExpectedHightestTxnAmt { get; set; }
    public int SalesAgent { get; set; }
    public int SalesManager { get; set; }
    public string? IsVip { get; set; }
    public string? IsHsbcMerchant { get; set; }
    public string? Iban { get; set; }
    public string? AccountNumber { get; set; }
    public string? BeneficiaryFullName { get; set; }

    public string? AcceptedPaymentMethods { get; set; }
    public string? TransactionCurrency { get; set; }
    public string? DCCProvider { get; set; }
    public string? OrderSetupFeePaymentMode { get; set; }
    public string? OrderSetupFeePaymentReference { get; set; }
    public string? OrderSecurityDepositPaymentMode { get; set; }
    public string? OrderSecurityDepositPaymentReference { get; set; }
    public int? SettlementTimeFrame { get; set; }
    public long? PerTransactionLimit { get; set; }
    public long? PerTransactionRefundLimit { get; set; }
    public string? TransactionType { get; set; }
}