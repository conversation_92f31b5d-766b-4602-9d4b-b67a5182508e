﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class PersonOfInterestCheck
{
    public Guid PersonCheckId { get; set; }

    public Guid MerchantPersonOfInterestId { get; set; }

    public string CheckType { get; set; } = string.Empty;

    public DateTime CheckDate { get; set; }

    public string CheckStatus { get; set; } = string.Empty;

    public int CheckScore { get; set; }

    public string? CheckProvider { get; set; }

    public string? CheckTriggerType { get; set; }
    public bool? IsFinscanTriggerAutomatic { get; set; }

    public DateTime ValidFrom { get; set; }

    public DateTime? ValidTo { get; set; }

    public ICollection<CheckComment> CheckComments { get; set; } = new List<CheckComment>();

    public ICollection<PersonCheckPayload> PersonCheckPayloads { get; set; } = new List<PersonCheckPayload>();

    public string CreatedBy { get; set; } = string.Empty;

    public string? UpdatedBy { get; set; }
}
