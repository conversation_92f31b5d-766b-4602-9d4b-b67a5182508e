﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;
using System;

namespace Common.Providers.Apex.Validations;

public class MerchantPersonOfInterestValidator : AbstractValidator<MerchantPersonOfInterest>
{
    public MerchantPersonOfInterestValidator()
    {
        RuleFor(person => person.FirstName)
            .NotEmpty()
            .WithErrorCode(Errors.FirstNameRequired.Code)
            .WithMessage(Errors.FirstNameRequired.Message);

#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
    }
}