{"AllowedHosts": "*", "ConfigService": {"Url": "http://uae-dev-config.uae-dev.gd-azure-dev.net", "VaultSecretsPath": "/usr/share/secrets/secrets.json", "Keys": ["ConfigService:VaultSecretsPath", "Serilog:MinimumLevel", "ServiceExceptions:UseSlim", "Default:RabbitMqConfig:*", "MessageClientConfiguration:*", "Default:HealthChecks:*", "Migrations:IsAutoMigrationEnabled", "MerchantClientConfiguration:*", "SftpClientConfiguration:*", "PostilionClientConfiguration:*", "BackgroundWorkersConfiguration:*", "UrlSettings:ProviderConfigServiceBaseUrl", "UrlSettings:RequestLogServiceBaseUrl", "Apex:*"]}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error"}}, "Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithProcessId", "WithProcessName"], "Properties": {"ApplicationName": "EnablementSyncService", "ProcessName": "EnablementSyncService"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "UrlSettings": {"ProviderConfigServiceBaseUrl": "http://uae-dev-nbconfiguration.uae-dev.gd-azure-dev.net", "RequestLogServiceBaseUrl": "http://uae-dev-log.uae-dev.gd-azure-dev.net"}, "Migrations": {"IsAutoMigrationEnabled": true}, "ServiceExceptions": {"UseSlim": false}, "Default": {"RabbitMqConfig": {"HostName": "localhost", "Port": "30567", "VirtualHost": "/", "UserName": "upgw-dev", "Password": "JU#dY6GR+py*8>LA", "SslEnabled": false}}, "MerchantClientConfiguration": {"Host": "localhost", "Port": "30567", "UserName": "upgw-dev", "Password": "JU#dY6GR+py*8>LA", "Path": ""}, "SftpClientConfiguration": {"Host": "", "Port": "22", "UserName": "", "Password": "", "Path": ""}, "PostilionClientConfiguration": {"Host": "", "Port": "22", "UserName": "", "Password": "", "Path": ""}, "BackgroundWorkersConfiguration": {"HourlyBackgroundWorkerConfig": {"CronExpression": "0 11,15,19 * * *", "Filename": "t_merchants"}}, "Apex": {"clientId": "5RbpkCihqsZLaECt2owurU1fxBo3UUzbcarVEllGhhE", "clientsecret": "SdwipsOm5gerDQ2YXHrbUgMayIjTvfWdQOUtHyZDBIg", "IgnoreApexSSLValidation": true}}