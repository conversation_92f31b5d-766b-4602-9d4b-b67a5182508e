﻿using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.Tests.ValidationTests
{
    public class MerchantExternalProductValidatorTests
    {
        private MerchantExternalProductValidator _validator;

        [SetUp]
        public void SetUp()
        {
            _validator = new MerchantExternalProductValidator();
        }

        [Test]
        public void Should_Have_Error_When_ProductCode_Is_Empty()
        {
            var model = new MerchantExternalProduct { ProductCode = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ProductCode)
                  .WithErrorCode(Errors.ProductCodeRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_Description_Is_Empty()
        {
            var model = new MerchantExternalProduct { Description = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.Description)
                  .WithErrorCode(Errors.DescriptionRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_PaymentMethod_Is_Empty()
        {
            var model = new MerchantExternalProduct { PaymentMethod = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.PaymentMethod)
                  .WithErrorCode(Errors.PaymentMethodRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_ChargeFrequency_Is_Empty()
        {
            var model = new MerchantExternalProduct { ChargeFrequency = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ChargeFrequency)
                  .WithErrorCode(Errors.ChargeFrequencyRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_ProductCategory_Is_Empty()
        {
            var model = new MerchantExternalProduct { ProductCategory = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ProductCategory)
                  .WithErrorCode(Errors.ProductCategoryRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_ChargeType_Is_Empty()
        {
            var model = new MerchantExternalProduct { ChargeType = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ChargeType)
                  .WithErrorCode(Errors.ChargeTypeRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_ProductType_Is_Empty()
        {
            var model = new MerchantExternalProduct { ProductType = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ProductType)
                  .WithErrorCode(Errors.ProductTypeRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_ProductGroup_Is_Empty()
        {
            var model = new MerchantExternalProduct { ProductGroup = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.ProductGroup)
                  .WithErrorCode(Errors.ProductGroupRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_CounterParty_Is_Empty()
        {
            var model = new MerchantExternalProduct { CounterParty = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.CounterParty)
                  .WithErrorCode(Errors.CounterPartyRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_DisplayOrder_Is_Less_Than_Zero()
        {
            var model = new MerchantExternalProduct { DisplayOrder = -1 };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.DisplayOrder)
                  .WithErrorCode(Errors.DisplayOrderNonNegative.Code);
        }

        [Test]
        public void Should_Have_Error_When_IsProofRequired_Is_Null()
        {
            var model = new MerchantExternalProduct { IsProofRequired = null };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.IsProofRequired)
                  .WithErrorCode(Errors.IsProofRequiredRequired.Code);
        }

        [Test]
        public void Should_Not_Have_Error_When_Model_Is_Valid()
        {
            var model = new MerchantExternalProduct
            {
                ProductCode = "ValidProductCode",
                Description = "ValidDescription",
                PaymentMethod = "ValidPaymentMethod",
                ChargeFrequency = "ValidChargeFrequency",
                ProductCategory = "ValidProductCategory",
                ChargeType = "ValidChargeType",
                ProductType = "ValidProductType",
                ProductGroup = "ValidProductGroup",
                CounterParty = "ValidCounterParty",
                DisplayOrder = 0,
                IsProofRequired = true
            };
            var result = _validator.TestValidate(model);
            result.ShouldNotHaveAnyValidationErrors();
        }
    }
}
