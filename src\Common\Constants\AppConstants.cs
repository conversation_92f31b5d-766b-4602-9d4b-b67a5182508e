﻿namespace Common.Constants;

public static class AppConstants
{
    public const string UnassignedProjectName = "UNASSIGNED";

    public const string MaxExpiryDate = "********";
    public const string ReadyToAssign = "01";
    public const string RiskLevel = "Low";
    public const string Zip = "0000";
    public const string MaxValue = "999999";
    public const string Zero = "0";
    public const string IsInter = "true";
    public const string One = "1";
    public const string BankName = "EBILAEAD";
    public const string Saudi = "Saudi Arabia";
    public const string Riyadh = "Riyadh";

    public const string SettleValue = "SETTLE";
    public const int Vat = 15;



    public const string BankType = "aggregator";

    public const string Payment = "B";
    public const string PaymentA = "A";

    public const string TT = "PURCHASE";
    public const string CT = "P";
    public const string VI = "VI";
    public const string VL = "VL";
    public const string ML = "ML";
    public const string MI = "MI";
    public const string DCCRS = "DCCRS";
    public const string Offset = "D";
    public const string FIX = "FIX";
    public const string typeB = "B";
    public const string typeP = "P";
    public const string VCI = "VCI";
    public const string MCI = "MCI";
    public const string TokenError = "invalid_token";
    public const string GEIDEA = "DEFAULT_BANK";

    public static class Availability
    {
        public const string Live = "Live";
        public const string Obsolete = "Obsolete";
    }
    public static class EconomicActivity
    {
        public const string PTSP = "PTSP";
        public const string Aggregator = "AGGREGATOR";
        public const string Acquirer = "ACQUIRER";

    }

    public static class BIC
    {
        public const string SABBSARI = "SABBSARI";
        public const string RJHISARI = "RJHISARI";
        public const string RIBLSARI = "RIBLSARI";

    }
    public static class CounterParty
    {
        public const string KSA = "GEIDEA_SAUDI";
        public const string UAE = "GEIDEA_UAE";
    }

    public static class Status
    {
        public const string Failed = "Failed";
        public const string Completed = "Completed";
        public const string Mapped = "Mapped";
        public const string Consumed = "Consumed";


    }

    public static class Flow
    {
        public const string Normal = "Normal";
        public const string HelpRequired = "HelpRequired";
        public const string Preorder = "Preorder";
    }

    public static class ChannelType
    {
        public const string ECOM = "ECOM";
        public const string PGW = "PGW";
        public const string CNP = "CNP";
        public const string HSBC = "HSBC";

    }
    public static class Providers
    {
        public const string Apex = "APEX";
    }

    public static class Actions
    {
        public const string Create = "Create";
        public const string Get = "Get";
        public const string Update = "Update";
        public const string Delete = "Delete";

        public const string GetToken = "GetToken";
        public const string CreateTerminal = "CreateTerminal";

    }

    public static class MerchantTypes
    {
        public const string Account = "S";
        public const string Business = "C";
        public const string Chain = "A";
        public const string AccountLevel = "3";

    }

    public static class Entity
    {
        public const string Merchant = "MERCHANT";
        public const string Chain = "Chain";
        public const string GetMerchant = "GETMERCHANT";
        public const string Account = "ACCOUNT";
        public const string Product = "PRODUCT";
        public const string Order = "ORDER";
        public const string Contact = "CONTACT";
        public const string Bank = "BANK";
        public const string PersonOfInterest = "PERSONOFINTEREST";
        public const string ShareHolder = "SHAREHOLDER";
        public const string Terminal = "TERMINAL";
        public const string Commission = "COMMISSION";
        public const string Address = "ADDRESS";
        public const string TransactionType = "Purchase";

    }

    public static class MerchantCreateAction
    {
        public const string Add = "A";
        public const string Update = "U";

    }

    public static class ReferenceCatalogNames
    {
        public const string Cities = "CITIES";
        public const string Governorates = "GOVERNORATES";
        public const string BusinessDomains = "BUSINESS_DOMAINS";
        public const string CityToGovernorate = "CITY_TO_GOVERNORATE";
        public const string ReferralChannel = "REFERRAL_CHANNEL";
        public const string SwiftCode = "SWIFT_CODE";
        public const string Banks = "BANKS";
        public const string MerchantTag = "MERCHANT_TAG";
        public const string ProductCodeToReferralChannel = "PRODUCT_CODE_TO_REFERAL_CHANNEL";
        public const string Product = "PRODUCT";
        public const string AquiringLedger = "ACQUIRING_LEDGER";
        public const string AquiringLedgerToGsdkLedger = "ACQUIRING_LEDGER_TO_GSDK_LEDGER";
        public const string IbanToSwiftCode = "IBAN_TO_SWIFT_CODE";
        public const string Areas = "AREAS";
        public const string AreasToCities = "AREAS_TO_CITIES";
        public const string MerchantType = "MERCHANT_TYPE";
        public const string NBECities = "NBE_BANK_CITIES";
        public const string MerchantCategoryCodesRef = "MCC";
        public const string CommercialLegalType = "COMMERCIAL_LEGAL_TYPE";
        public const string AccountsChannel = "ACCOUNTS_CHANNEL";
        public const string AcceptedPaymentMethods = "ACCEPTED_PAYMENT_METHODS";//need to add options
        public const string PayoutSchedule = "PAYOUT_SCHEDULE";
        public const string FeePaymentModeRef = "FEE_PAYMENT_MODE";
        public const string CommissionType = "COMMISSION_TYPE";
        public const string ProductCodeReference = "PRODUCT_CODE";
        public const string ProductBundle = "PRODUCT_BUNDLE";
        public const string SwiftCodeToBank = "SWIFT_CODE_TO_BANK";
        public const string Accepted_Currencies = "ACCEPTED_CURRENCIES";
        public const string Office_Location_Type = "OFFICE_LOCATION_TYPE";
        public const string MccToBusinessDomain = "MCC_TO_BUSINESS_DOMAINS";
        public const string Products_Card_Present_Mode = "PRODUCTS_CARD_PRESENT_MODE";
        public const string Countries = "COUNTRIES";
        public const string Nationality = "NATIONALITY";
        public const string Limited = "LIMITED";
        public const string BusinessLicenseType = "BUSINESS_LICENSE_TYPE";
        public const string TransactionType = "TRANSACTION_TYPE";
    }
    public static class RabbitMqSenders
    {
        public const string ApexResponseSender = "Geidea.NexusBridge.Adapter";
    }

    public static class RabbitMqExchanges
    {
        public const string ApexResponseExchange = "NexusBridge.Apex.Response";
    }

    public static class RabbitMqQueues
    {
        public const string ApexResponseQueue = "NexusBridge.Apex.Response";
    }

    public static class RabbitMqRoutingKeys
    {
        public const string ApexResponseRoutingKey = "NexusBridge.Apex.Response";
    }
}