﻿using System;

namespace Common.Models
{
    public class MerchantDccSet
    {
        public string? ProductCode { get; set; }
        public string? CommissionType { get; set; }
        public string? TransactionType { get; set; }

        public string? PercentageAmount { get; set; }
        public string? FlatAmount { get; set; }
        public string? MinCommAmount { get; set; }
        public string? MaxCommAmount { get; set; }
        public int? VatPercentage { get; set; }
        public string? ScaleProfile { get; set; }
        public string? ValidFrom { get; set; }
        public string? ValidTo { get; set; }
        public string? IsInterchange { get; set; }
        public string? PremiumPercentageAmount { get; set; }
        public string? PremiumFlatAmount { get; set; }

    }
}