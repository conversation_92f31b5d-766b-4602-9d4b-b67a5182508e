﻿using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.Tests.ValidationTests;

public class MerchantPersonOfInterestValidatorTests
{
    private MerchantPersonOfInterestValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new MerchantPersonOfInterestValidator();
    }

    [Test]
    public void Should_Have_Error_When_FirstName_Is_Empty()
    {
        var model = new MerchantPersonOfInterest { FirstName = string.Empty };
        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.FirstName)
              .WithErrorCode(Errors.FirstNameRequired.Code);
    }

    [Test]
    public void Should_Not_Have_Error_When_Model_Is_Valid()
    {
        var model = new MerchantPersonOfInterest
        {
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "123456789",
            Fax = "987654321",
            NationalId = "AB123456",
            MobileNumber = "1234567890",
            DOB = DateTimeOffset.UtcNow.AddYears(-30),
            Address = new PersonOfInterestAddress
            {
                Street = "123 Main St",
                City = "Anytown",
                District = "District",
                Zip = "12345",
                Country = "Anyland"
            }
        };
        var result = _validator.TestValidate(model);
        result.ShouldNotHaveAnyValidationErrors();
    }
}
