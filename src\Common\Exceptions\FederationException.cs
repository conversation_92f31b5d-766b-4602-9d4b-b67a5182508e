﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.AppConstants;

namespace Common.Exceptions
{
    public class FederationException : Exception
    {
        public string Level { get; set; }
        public string Provider { get; set; }
        public FederationException(string level, string provider, string message) : base(message)
        {
            Level = level;
            Provider = provider;
        }
        public FederationException(string level, string provider, string message, Exception innerException) : base(message, innerException)
        {
            Level = level;
            Provider = provider;
        }
    }
}