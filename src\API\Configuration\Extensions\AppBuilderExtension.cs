﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Services.Messaging;
using System.Diagnostics.CodeAnalysis;

namespace API.Configuration.Extensions;

[ExcludeFromCodeCoverage]
public static class AppBuilderExtension
{
    public static IApplicationBuilder StartMessageConsumption(this IApplicationBuilder builder)
    {
        var messageExchangeConsumer = builder.ApplicationServices.GetService<MerchantExchangeConsumer>();
        messageExchangeConsumer?.BeginReceive();

        var messageOrderExchangeConsumer = builder.ApplicationServices.GetService<OrderExchangeConsumer>();
        messageOrderExchangeConsumer?.BeginReceive();

        var messageProductExchangeConsumer = builder.ApplicationServices.GetService<ProductExchangeConsumer>();
        messageProductExchangeConsumer?.BeginReceive();

        return builder;
    }
}