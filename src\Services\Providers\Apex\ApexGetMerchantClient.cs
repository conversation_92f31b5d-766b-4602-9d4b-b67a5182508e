﻿using AutoMapper;
using Common.Constants;
using Common.Models;
using Common.Models.Apex;
using Common.Models.Configuration;
using Common.Options;
using Common.Providers.Apex.Validations;
using Common.Services;
using Geidea.Utils.Messaging.Base;
using Geidea.Messages.Merchant;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Services.Helper;
using Services.Helper.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FluentValidation;

namespace Services.Providers.Apex
{
    public class ApexGetMerchantClient : IApexGetMerchantClient
    {
        private readonly ILogger<ApexService> logger;
        private readonly HttpClient httpClient;
        private readonly IProviderService providerService;
        private readonly IRequestLogService requestLogService;
        private readonly IMapper mapper;
        private readonly IHttpContextAccessor contextAccessor;
        private readonly string correlationId;
        private readonly IOptionsMonitor<UrlSettings> urlOptions;
        private readonly IHttpRequestExecutorService httpRequestExecutorService;
        private readonly IHttpClientWrapper httpClientWrapper;
        private readonly IRequestHeaders requestHeaders;
        private readonly IApexToken token;

        private readonly IConfiguration configuration;
        private const string XCorrelationIdHeader = "X-Correlation-ID";
        private const string RequestIdHeader = "Request-ID";
        private const string ApplicationLanguageHeader = "X-ApplicationLanguage";
        private const string CounterpartyCodeHeader = "X-CounterpartyCode";

        public ApexGetMerchantClient(
            HttpClient httpClient,
            ILogger<ApexService> logger,
            IProviderService providerService,
            IRequestLogService requestLogService,
            IMapper mapper,
            IHttpContextAccessor contextAccessor,
            IOptionsMonitor<UrlSettings> urlOptions,
            IHttpRequestExecutorService httpRequestExecutorService, IConfiguration configuration, IRequestHeaders requestHeaders, IHttpClientWrapper httpClientWrapper, IApexToken token)
        {
            this.logger = logger;
            this.httpClient = httpClient;
            this.providerService = providerService;
            this.requestLogService = requestLogService;
            this.httpClientWrapper = httpClientWrapper;
            this.mapper = mapper;
            this.contextAccessor = contextAccessor;
            this.urlOptions = urlOptions;
            this.httpRequestExecutorService = httpRequestExecutorService;
            var headerCorrelationId = contextAccessor.HttpContext?.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName];
            correlationId = headerCorrelationId.HasValue ? headerCorrelationId.Value.ToString() : Guid.Empty.ToString();
            this.configuration = configuration;
            this.requestHeaders = requestHeaders;
            this.token = token;
        }

        // Used to check if the business or account is already present in APEX.
        public async Task<bool> CheckIfExists(MerchantAction merchant, string accessToken, string url, GenericMessage<Common.Models.Merchant> header, ProviderQueueConfigResponseModel providerConfig)
        {
            logger.LogInformation("Checking if merchantId exists");
            
            bool response = false;
            var payload = new MerchantRequest()
            {
                merchantId = merchant.Merchant.MerchantId,
            };

            var finalPayload = JsonConvert.SerializeObject(payload);

            HttpContent content = new StringContent(finalPayload, Encoding.UTF8)
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            Dictionary<string, string>? headers = new() { { "Authorization", "Bearer " + accessToken } };
            
            try
            {
                response = await SendGetMerchantRequest(url, headers, content, header, payload);
                logger.LogInformation("Merchant exist: {@response}", response);
            }
            catch (Exception ex)
            {
                logger.LogCritical("Error when calling Apex Get Merchant. Error was {Error}", ex.Message);
                if (ex.Message.Contains(AppConstants.TokenError))
                {
                    var newAccessToken = await token.GetTokenAsync(providerConfig, merchant!, header.Header.CorrelationId, header.Data.MerchantDetails.MID, AppConstants.Entity.Order);
                    Dictionary<string, string>? newHeaders = new Dictionary<string, string>
                    {
                        { "Authorization", "Bearer " +newAccessToken.access_token },
                    };
                    // Retry the request with the new access token
                    response = await SendGetMerchantRequest(url, newHeaders, content, header, payload);
                }

                var requestDetails = new
                {
                    Url = url,
                    HttpMethod = "GET",
                    ContentType = content,
                    Content = await content.ReadAsStringAsync(),
                    Headers = headers,
                    RequestDate = DateTime.UtcNow
                };

                string requestMetadata = JsonConvert.SerializeObject(requestDetails);
                await requestLogService.CreateRequestLogAsync(new Models.RequestLog()
                {
                    CorrelationId = header.Header.CorrelationId,
                    ParentCorrelationId = header.Header.CorrelationId,
                    Status = AppConstants.Status.Failed,
                    Entity = AppConstants.Entity.GetMerchant,
                    EntryDate = DateTime.UtcNow,
                    RequestId = RequestIdHeader,
                    RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(requestMetadata)),
                    RequestType = AppConstants.Actions.Create,
                    ResponseMetaData = ex.Message,
                });

                return response;
            }

            return response;
        }

        private async Task<bool> SendGetMerchantRequest(string url, Dictionary<string, string> headers, HttpContent content, GenericMessage<Common.Models.Merchant> header, MerchantRequest payload)
        {
            var response = await httpClientWrapper.SendHttpPostRequest(url, content, headers);

            if (response != null)
            {
                var jsonResult = await response.Content.ReadAsStringAsync();
                logger.LogInformation("Recieved response from Apex Get Merchant. Response was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling Apex Get Merchant. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);
                    throw new PassthroughException(response);
                }

                try
                {
                    var json = JsonConvert.DeserializeObject<MerchantAction>(jsonResult);
                    await requestLogService.CreateRequestLogAsync(new Models.RequestLog()
                    {
                        CorrelationId = header.Header.CorrelationId,
                        ParentCorrelationId = header.Header.CorrelationId,
                        Status = AppConstants.Status.Completed,
                        Entity = AppConstants.Entity.GetMerchant,
                        EntryDate = DateTime.UtcNow,
                        RequestId = RequestIdHeader,
                        RequestType = AppConstants.Actions.Create,
                        RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(payload))),
                        ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult)),
                    });

                    if (json.Merchant != null)
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogInformation("Error when calling Apex Get Merchant. Error was {Error}", ex.Message);
                    return true;
                }
            }
            return false;
        }
    }
}