﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services.Helper.Interface
{
    public interface IHttpClientWrapper
    {
        public Task<HttpResponseMessage> SendHttpGetRequest(string requestUri, HttpContent httpContent,Dictionary<string, string>? headers = null);

        public Task<HttpResponseMessage> SendHttpPostRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null);
        public Task<HttpResponseMessage> SendHttpPostRequestForGetMerchant(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null);

        public Task<HttpResponseMessage> SendHttpPutRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null);

        public Task<HttpResponseMessage> SendHttpPatchRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null);

    }
}
