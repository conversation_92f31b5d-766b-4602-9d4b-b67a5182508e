﻿using Geidea.Utils.Counterparty.Providers;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.AppConstants;
using Common.Models.Apex;
using Common.Services;
using Common.Exceptions;
using Geidea.Utils.Messaging;
using Geidea.Utils.Exceptions;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Newtonsoft.Json;
using System.Net.Http;
using Services.Providers.Apex;
using Common.Models.Configuration;
using Common.Providers.Apex.Validations;
using Common.Constants;
using System.Net;
using AutoMapper;
using Common.Options;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Common.Models;
using Services.Helper.Interface;
using Geidea.Utils.Messaging.Base;
using Services.Models;

namespace Services.Tests
{
    public class ApexBusinessTests
    {
        private readonly IHttpContextAccessor contextAccessor = Substitute.For<IHttpContextAccessor>();
        private IRequestLogService requestLogService = Substitute.For<IRequestLogService>();
        private readonly ILogger<ApexService> logger = Substitute.For<ILogger<ApexService>>();
        private IHttpRequestExecutorService httpRequestExecutorService = Substitute.For<IHttpRequestExecutorService>();
        private IHttpClientWrapper httpClientWrapper = Substitute.For<IHttpClientWrapper>();
        private IApexTerminalClient apexTerminal = Substitute.For<IApexTerminalClient>();
        private IRequestHeaders requestHeaders = Substitute.For<IRequestHeaders>();
        private HttpClient httpClient = Substitute.For<HttpClient>();
        private IProviderService providerService = Substitute.For<IProviderService>();
        private IMapper mapper = Substitute.For<IMapper>();
        private IOptionsMonitor<UrlSettings> urlOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
        private IConfiguration configuration;
        private ApexBusinessAccountClient apexBusiness;
        private IApexToken token = Substitute.For<IApexToken>();

        [SetUp]
        public void Setup()
        {
            var configurationBuilder = new ConfigurationBuilder()
           .AddInMemoryCollection(new Dictionary<string, string>
           {
                { "Apex:ClientId", "SomeValue" },
                { "Apex:ClientSecret", "SomeValue" },

           });

            configuration = configurationBuilder.Build();
            apexBusiness = new ApexBusinessAccountClient(httpClient, logger, providerService, requestLogService, mapper, contextAccessor, urlOptions, httpRequestExecutorService, configuration, requestHeaders, httpClientWrapper, token);
            var httpContext = Substitute.For<HttpContext>();
            httpContext.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName] = "test-correlation-id";
            contextAccessor.HttpContext.Returns(httpContext);
        }

        [Test]
        public async Task CreateMerchantMessage_ShouldCreateRequestLog_When_API_Throws_Error()
        {
            // Arrange
            var merchant = new GenericMessage<Common.Models.Merchant>
            {
                Header = new Header()
                {
                    CorrelationId = new Guid(),
                },
                Data = new Common.Models.Merchant()
                {
                    MerchantDetails = new Common.Models.MerchantDetails
                    {
                        MID = "***********",
                        MCC = "m",
                        AcquiringLedger = "m",
                        DefaultLanguage = "d",
                        StoreName = "m",
                        BusinessDomain = "m",
                        FoundationDate = DateTime.Now,
                        AlternativeStoreName = "m",
                        MerchantSegment = "m",
                        AlternativeMerchantName = "m",
                        AdditionalTradingInformation = "m",
                        BusinessId = "m",
                        BusinessType = "m",
                        CompanyPhoneNumber = "********",
                        TLExpiryDate = DateTime.Now.AddYears(5),
                        TLIssueDate = DateTime.Now.AddYears(-4),
                        LegalName = "m",
                        Nickname = "m",
                        RegistrationNumber = "m",
                        RiskLevel = "m",
                    },
                    MerchantId = new Guid("b653aa1c-dfd5-44f2-991a-08dcac7645fc"),
                    MerchantLevel = "1",
                    AccountConfig = new Common.Models.MerchantAccountConfig()
                    {
                        MerchantId = new Guid(),
                        SalesAgent = 1,
                        DCCProvider = "1",
                        PayoutDay = 0,
                        PayoutBank = "nn",
                        PayoutCapAmount = 0,
                        PayoutSchedule = "1",
                        SettlementCurrency = "USD",
                        AcceptedPaymentMethods = "VSC",
                        IsHsbcMerchant = "1",
                        Bic = "Bank",
                        AccountNumber = "1",
                        BeneficiaryFullName = "1",
                    },
                    MerchantTerminals = new List<Common.Models.MerchantTerminal>()
                {
                    new Common.Models.MerchantTerminal()
                    {
                        TerminalId ="123",
                        TerminalStatus ="01",
                        SerialNumber ="1",
                        Brand ="1",
                        MerchantId ="12",
                        Model ="1eds",
                    },
                    new Common.Models.MerchantTerminal()
                    {
                        TerminalId ="123",
                        TerminalStatus ="01",
                        SerialNumber ="1",
                        Brand ="1",
                        MerchantId ="12",
                        Model ="1eds",
                    },
                },
                    MerchantStatus = "12",
                    MerchantType = "mm",
                    CommissionTypes = new List<MerchantCommissionConfig>()
                {
                    new MerchantCommissionConfig()
                    {
                        ProductCode = "A",
                        MaxCommAmount ="1",
                        MinCommAmount ="0",
                        CommissionType ="12",
                        FlatAmount ="1",
                        IsInterchange ="N",
                        TransactionType ="12",
                        ScaleProfile ="N",
                        PercentageAmount ="1",
                        VatPercentage =1,
                        ValidFrom ="1",
                        ValidTo ="1",
                        Value =0,
                        MerchantId =new Guid()
                    }
                }
                }
            };
            var providerConfig = new List<ProviderQueueConfigResponseModel>
            {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/GetMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "CreateTerminal", Entity = "MERCHANT",TargetURL = "/createTerminal" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "GetToken", Entity = "MERCHANT",TargetURL = "/generate/token" },

            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "GetToken", Entity = "Chain",TargetURL = "/generate/token" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Get", Entity = "Chain",TargetURL = "/merchant-inquiry/getMerchantInformation" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createTerminal" },
            };

            var responseToken = new TokenResponse { access_token = "access_token" };
            var mockResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonConvert.SerializeObject(responseToken))
            };
            httpClientWrapper.SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>())
                .Returns(Task.FromResult(mockResponse));

            // Act
            await apexBusiness.CreateBusinessOrAccount(merchant, providerConfig, new MerchantAction(), new TokenResponse());

            // Assert
            await requestLogService.Received().CreateRequestLogAsync(Arg.Any<Models.RequestLog>());
        }

        [Test]
        public async Task Test_CatchBlock_TokenError()
        {
            // Arrange
            var merchant = new MerchantAction()
            {
                Merchant = new Common.Models.Apex.Merchant()
                {
                    MerchantId = "123",
                }
            };
            var providerConfig = new List<ProviderQueueConfigResponseModel>
            {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl="https://10.245.88.105:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/GetMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "CreateTerminal", Entity = "MERCHANT",TargetURL = "/createTerminal" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "GetToken", Entity = "MERCHANT",TargetURL = "/generate/token" },

            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "GetToken", Entity = "Chain",TargetURL = "/generate/token" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Get", Entity = "Chain",TargetURL = "/merchant-inquiry/getMerchantInformation" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Create", Entity = "Chain",TargetURL = "/merchant-onboarding/createTerminal" },
            };

            var mockAccessToken = new TokenResponse { access_token = "new-access-token" };
            httpClientWrapper.SendHttpPostRequest(Arg.Any<string>(), Arg.Any<HttpContent>(), Arg.Any<Dictionary<string, string>>())
             .Returns(Task.FromResult(new HttpResponseMessage(HttpStatusCode.Unauthorized)));

            token.GetTokenAsync(Arg.Any<ProviderQueueConfigResponseModel>(), Arg.Any<Common.Models.Apex.MerchantAction>(), Arg.Any<Guid>(), Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.FromResult(mockAccessToken));

            // Act & Assert
            var ex = Assert.ThrowsAsync<FederationException>(async () =>
                await apexBusiness.CreateBusinessOrAccount(new GenericMessage<Common.Models.Merchant>(), providerConfig, merchant, new TokenResponse()));

            // Verify the exception properties
            Assert.That(ex.Level, Is.EqualTo("Business"));
            Assert.That(ex.Provider, Is.EqualTo("APEX"));
            Assert.That(ex.Message, Does.Contain("APEX API call failed"));
            Assert.That(ex.InnerException, Is.TypeOf<PassthroughException>());

            // Verify that request log was created
            await requestLogService.Received().CreateRequestLogAsync(Arg.Any<RequestLog>());
        }
    }
}
