﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class MerchantExternalProductValidator : AbstractValidator<MerchantExternalProduct>
{
    public MerchantExternalProductValidator()
    {
        RuleFor(x => x.ProductCode)
            .NotEmpty()
            .WithErrorCode(Errors.ProductCodeRequired.Code)
            .WithMessage(Errors.ProductCodeRequired.Message);

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithErrorCode(Errors.DescriptionRequired.Code)
            .WithMessage(Errors.DescriptionRequired.Message);

        RuleFor(x => x.PaymentMethod)
            .NotEmpty()
            .WithErrorCode(Errors.PaymentMethodRequired.Code)
            .WithMessage(Errors.PaymentMethodRequired.Message);

        RuleFor(x => x.ChargeFrequency)
            .NotEmpty()
            .WithErrorCode(Errors.ChargeFrequencyRequired.Code)
            .WithMessage(Errors.ChargeFrequencyRequired.Message);

        RuleFor(x => x.ProductCategory)
            .NotEmpty()
            .WithErrorCode(Errors.ProductCategoryRequired.Code)
            .WithMessage(Errors.ProductCategoryRequired.Message);

        RuleFor(x => x.ChargeType)
            .NotEmpty()
            .WithErrorCode(Errors.ChargeTypeRequired.Code)
            .WithMessage(Errors.ChargeTypeRequired.Message);

        RuleFor(x => x.ProductType)
            .NotEmpty()
            .WithErrorCode(Errors.ProductTypeRequired.Code)
            .WithMessage(Errors.ProductTypeRequired.Message);

        RuleFor(x => x.ProductGroup)
            .NotEmpty()
            .WithErrorCode(Errors.ProductGroupRequired.Code)
            .WithMessage(Errors.ProductGroupRequired.Message);

        RuleFor(x => x.CounterParty)
            .NotEmpty()
            .WithErrorCode(Errors.CounterPartyRequired.Code)
            .WithMessage(Errors.CounterPartyRequired.Message);

        RuleFor(x => x.DisplayOrder)
            .GreaterThanOrEqualTo(0)
            .WithErrorCode(Errors.DisplayOrderNonNegative.Code)
            .WithMessage(Errors.DisplayOrderNonNegative.Message);

        RuleFor(x => x.IsProofRequired)
            .NotNull()
            .WithErrorCode(Errors.IsProofRequiredRequired.Code)
            .WithMessage(Errors.IsProofRequiredRequired.Message);
    }
}