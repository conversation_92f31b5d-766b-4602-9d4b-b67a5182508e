﻿//using FluentAssertions;
//using Geidea.LogService.Services.Exceptions;
//using Geidea.LogService.Services.Interfaces;
//using Geidea.LogService.Services.Models;
//using Geidea.Utils.Exceptions;
//using Microsoft.AspNetCore.JsonPatch;
//using NSubstitute;
//using NSubstitute.ExceptionExtensions;
//using NUnit.Framework;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;

//namespace Geidea.LogService.Services.Tests
//{
//    public class LeadServiceTests
//    {
//        private ILeadRepository repository;
//        private ILeadService leadService;
        
//        private readonly LeadResponse leadResponse = new LeadResponse
//        {
//            LeadId = Guid.NewGuid(),
//            LeadStatus = "New",
//            PhoneNumber = "700000001",
//            CountryPrefix= "+40"
//        };

//        private readonly LeadUpdateRequest leadUpdate = new LeadUpdateRequest
//        {
//            LeadId = Guid.NewGuid(),
//            LeadStatus = "New",
//            PhoneNumber = "700000002",
//            CountryPrefix = "+40"
//        };

//        private readonly LeadCreateRequest leadCreate = new LeadCreateRequest
//        {
//            LeadStatus = "New",
//            PhoneNumber = "700000003",
//            CountryPrefix = "+40"
//        };


//        [SetUp]
//        public void Setup()
//        {
//            repository = Substitute.For<ILeadRepository>();
//            leadService = new LogService(repository);
//        }

//        [Test]
//        public async Task Create()
//        {
//            repository.Create(leadCreate).Returns(leadResponse);

//            var response = await leadService.Create(leadCreate);

//            await repository.Received(1).Create(leadCreate);
//            response.Should().BeEquivalentTo(leadResponse);
//        }

//        [Test]
//        public async Task Update()
//        {
//            repository.Update(leadUpdate).Returns(leadResponse);

//            var response = await leadService.Update(leadUpdate);

//            await repository.Received(1).Update(leadUpdate);
//            response.Should().BeEquivalentTo(leadResponse);
//        }

//        [Test]
//        public async Task Update_ThrowsError()
//        {
//            repository.Update(leadUpdate).Throws(new ServiceException(Error.LeadNotFound));

//            Assert.ThrowsAsync<ServiceException>(() => leadService.Update(leadUpdate));
//            await repository.Received(1).Update(leadUpdate);
//        }

//        [Test]
//        public async Task Get()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");
            
//            repository.Get(leadId).Returns(leadResponse);

//            var response = await leadService.Get(leadId);

//            await repository.Received(1).Get(leadId);
//            response.Should().BeEquivalentTo(leadResponse);
//        }

//        [Test]
//        public async Task Get_ThrowsError()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");

//            repository.Get(leadId).Throws(new ServiceException(Error.LeadNotFound));

//            Assert.ThrowsAsync<ServiceException>(() => leadService.Get(leadId));
//            await repository.Received(1).Get(leadId);
//        }

//        [Test]
//        public async Task Delete()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");

//            repository.Delete(leadId).Returns(Task.CompletedTask);
//            await leadService.Delete(leadId);

//            await repository.Received(1).Delete(leadId);
//        }

//        [Test]
//        public async Task Delete_ThrowsError()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");

//            repository.Delete(leadId).Throws(new ServiceException(Error.LeadNotFound));

//            Assert.ThrowsAsync<ServiceException>(() => leadService.Delete(leadId));
//            await repository.Received(1).Delete(leadId);
//        }

//        [Test]
//        public async Task Patch()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");
//            var leadUpdate = new JsonPatchDocument<LeadRequest>();

//            repository.Patch(leadId, leadUpdate).Returns(leadResponse);

//            var response = await leadService.Patch(leadId, leadUpdate);

//            await repository.Received(1).Patch(leadId, leadUpdate);
//            response.Should().BeEquivalentTo(leadResponse);
//        }

//        [Test]
//        public async Task Patch_ThrowsError()
//        {
//            var leadId = Guid.Parse("00000000-0000-0000-0000-000000000001");
//            var leadUpdate = new JsonPatchDocument<LeadRequest>();

//            repository.Patch(leadId, leadUpdate).Throws(new ServiceException(Error.LeadNotFound));

//            Assert.ThrowsAsync<ServiceException>(() => leadService.Patch(leadId, leadUpdate));
//            await repository.Received(1).Patch(leadId, leadUpdate);
//        }

//        [Test]
//        public async Task GetLeadCreationDates()
//        {
//            repository.GetLeadCreationAsync(Arg.Any<IdsRequest>()).Returns(new List<LeadCreationResponse>
//            {
//                new LeadCreationResponse{LeadId = Guid.NewGuid(), CreatedDate=DateTime.Now},
//                new LeadCreationResponse{LeadId = Guid.NewGuid(), CreatedDate=DateTime.Now.AddDays(-1)}
//            });

//            var leads = await leadService.GetLeadCreationAsync(new IdsRequest { Ids = new Guid[] { Guid.NewGuid() } });

//            leads.Should().NotBeEmpty();
//            leads.Count.Should().Be(2);
//        }


//        [Test]
//        public async Task UpdateSalesIdAsync_CallsCorrectRepositoryMethod()
//        {
//            var salesIdUpdateRequest = new SalesIdUpdateRequest { InitialSalesId = "GTV0001", UpdatedSalesId = "GTV0002"};

//            repository.UpdateSalesIdAsync(salesIdUpdateRequest).Returns(Task.CompletedTask);
//            await leadService.UpdateSalesIdAsync(salesIdUpdateRequest);

//            await repository.Received(1).UpdateSalesIdAsync(salesIdUpdateRequest);
//        }

//        [Test]
//        public async Task WhenBulkCreate_ShouldCallRepositoryToBulkCreateLeads()
//        {
//            var leadsRequest = new List<LeadImportDto>() { new LeadImportDto() };

//            await leadService.BulkCreateAsync(leadsRequest);

//            await repository.Received(1).BulkCreateAsync(leadsRequest);
//        }

//        [Test]
//        public async Task GetLeadsIndentificationInformationAsync_WhenLeadsExists_ReturnsNonEmptyList()
//        {
//            var leads = new List<LeadIdentificationInformationResponse> { 
//                new LeadIdentificationInformationResponse()
//            };
//            repository.GetLeadsIdentificationInformationAsync().Returns(leads);
//            var response = await leadService.GetLeadsIndentificationInformationAsync();

//            Assert.IsTrue(response.Count > 0);
//        }

//        [Test]
//        public async Task GetLeadByPhoneAsync_CallsCorrectRepositoryMethod()
//        {
//            var leadPhoneRequest = new LeadPhoneRequest { CountryPrefix = "+966", PhoneNumber = "41257455" };

//            repository.GetLeadByPhoneAsync(leadPhoneRequest).Returns(Task.FromResult(leadResponse));
//            var result = await leadService.GetLeadByPhoneAsync(leadPhoneRequest);

//            result.Should().BeEquivalentTo(leadResponse);
//            await repository.Received(1).GetLeadByPhoneAsync(leadPhoneRequest);
//        }

//        [Test]
//        public async Task UpdatePhoneAsync_CallsCorrectRepositoryMethod()
//        {
//            var phoneUpdateRequest = new PhoneUpdateRequest { CountryPrefix = "+966", PhoneNumber = "41257455", LeadId = Guid.NewGuid()};
            
//            await leadService.UpdatePhoneAsync(phoneUpdateRequest);
            
//            await repository.Received(1).UpdatePhoneAsync(phoneUpdateRequest);
//        }

//        [Test]
//        public async Task UpdateLeadFromMerchantMigration_CallsCorrectRepositoryMethod()
//        {
//            var updateLeadRequest = new LeadIdentityMigrationUpdateDto
//            {
//                Id = Guid.NewGuid(),
//                FirstName = "Test Update Migration"
//            };

//            await leadService.UpdateLeadFromMerchantMigrationAsync(updateLeadRequest, CancellationToken.None);

//            await repository.Received(1).UpdateLeadFromMerchantMigrationAsync(Arg.Is<LeadIdentityMigrationUpdateDto>(x =>
//                x.Id == updateLeadRequest.Id && x.FirstName == updateLeadRequest.FirstName));
//        }

//        [Test]
//        public async Task UpdateLeadFromMerchantMigration_ThrowsError()
//        {
//            repository.ClearReceivedCalls();
//            var leadId = Guid.NewGuid();
//            var leadDto = new LeadIdentityMigrationUpdateDto {Id = leadId};

//            repository.UpdateLeadFromMerchantMigrationAsync(
//                    Arg.Is<LeadIdentityMigrationUpdateDto>(x => x.Id == leadId))
//                .Throws(new ServiceException(Error.LeadNotFound));


//            await leadService.Invoking(l =>
//                l.UpdateLeadFromMerchantMigrationAsync(leadDto))
//                .Should()
//                .ThrowAsync<ServiceException>();

//            await repository.Received(1).UpdateLeadFromMerchantMigrationAsync(leadDto);
//        }

//        [Test]
//        public async Task SearchBulkLeadsByFilters_CallsCorrectRepositoryMethod()
//        {
//            var leadSearchRequest = new LeadIdentityMigrationFilterRequest
//            {
//                ReferralChannel = "MERCHANT-MIGRATION",
//                LeadIds = new List<Guid> {Guid.NewGuid(), Guid.NewGuid()},
//                NationalIds = new List<string> {"12314212", "5434221"}
//            };

//            repository.SearchBulkLeadsByFiltersAsync(leadSearchRequest)
//                .Returns(new List<LeadIdentityMigrationSearchResponseDto> {new(), new()});

//            var response = await leadService.SearchBulkLeadsByFiltersAsync(leadSearchRequest);

//            await repository.Received(1).SearchBulkLeadsByFiltersAsync(Arg.Is<LeadIdentityMigrationFilterRequest>(x =>
//                x.ReferralChannel == leadSearchRequest.ReferralChannel && x.LeadIds == leadSearchRequest.LeadIds));
//            response.Count.Should().Be(2);
//        }

//        [Test]
//        public async Task SearchBulkLeadsByFilters_ThrowsError()
//        {
//            var leadSearchRequest = new LeadIdentityMigrationFilterRequest
//            {
//                LeadIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() },
//            };

//            repository.SearchBulkLeadsByFiltersAsync(leadSearchRequest)
//                .Throws(new ServiceException(Error.LeadNotFound));

//            Assert.ThrowsAsync<ServiceException>(() => leadService.SearchBulkLeadsByFiltersAsync(leadSearchRequest));
//            await repository.Received(1).SearchBulkLeadsByFiltersAsync(leadSearchRequest);
//        }

//        [Test]
//        public async Task GetLeadDetailsForMerchantMigrationAsync_CallsCorrectRepositoryMethod()
//        {
//            var request = new MerchantMigrationLeadDetailsRequest()
//            {
//                Email = new List<string>() { "<EMAIL>" },
//                Phone = new List<string>() { "41257455" },
//                NationalId = new List<string>() { "1234567890" }
//            };

//            var response = new List<MerchantMigrationLeadDetailsResponse>()
//            {
//                new MerchantMigrationLeadDetailsResponse()
//                {
//                    Email = "<EMAIL>",
//                    Phone = "41257455",
//                    NationalId = "1234567890"
//                }
//            };

//            repository.GetLeadDetailsForMerchantMigrationAsync(request).Returns(Task.FromResult(response));
//            var result = await leadService.GetLeadDetailsForMerchantMigrationAsync(request);

//            result.Should().BeEquivalentTo(response);
//            await repository.Received(1).GetLeadDetailsForMerchantMigrationAsync(request);
//        }

//        [Test]
//        public async Task GetExistingMerchantMigrationLeadsByNationalIds()
//        {
//            var nationalIds = new List<string>()
//            {
//                "1234567890"
//            };
//            var repositoryResponse = new List<ExistingOrderMigrationLeadResponse>()
//            {
//                new ExistingOrderMigrationLeadResponse()
//                {
//                    LeadId = Guid.NewGuid(),
//                    NationalId = nationalIds.First()
//                }
//            };
//            repository.GetExistingMerchantMigrationLeadsByNationalIds(nationalIds).Returns(repositoryResponse);

//            var result = await leadService.GetExistingMerchantMigrationLeadsByNationalIds(nationalIds);

//            result.Should().BeEquivalentTo(repositoryResponse);
//            await repository.Received(1).GetExistingMerchantMigrationLeadsByNationalIds(nationalIds);
//        }
//    }
//}
