﻿using AutoMapper;
using Common.Constants;
using Common.Models;
using Common.Models.Apex;
using Common.Models.Configuration;
using Common.Options;
using Common.Providers.Apex.Validations;
using Common.Services;
using Geidea.Utils.Messaging.Base;
using Geidea.Messages.Merchant;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Services.Helper;
using Services.Helper.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Services.Models;
using Common.Exceptions;

namespace Services.Providers.Apex
{
    public class ApexTerminalClient : IApexTerminalClient
    {
        private readonly ILogger<ApexService> logger;
        private readonly HttpClient httpClient;
        private readonly IProviderService providerService;
        private readonly IApexToken apexToken;
        private readonly IRequestLogService requestLogService;
        private readonly IMapper mapper;
        private readonly IHttpContextAccessor contextAccessor;
        private readonly string correlationId;
        private readonly IOptionsMonitor<UrlSettings> urlOptions;
        private readonly IHttpRequestExecutorService httpRequestExecutorService;
        private readonly IHttpClientWrapper httpClientWrapper;
        private readonly IRequestHeaders requestHeaders;
        private readonly IConfiguration configuration;
        private const string XCorrelationIdHeader = "X-Correlation-ID";
        private const string RequestIdHeader = "Request-ID";
        private const string ApplicationLanguageHeader = "X-ApplicationLanguage";
        private const string CounterpartyCodeHeader = "X-CounterpartyCode";

        public ApexTerminalClient(
            HttpClient httpClient,
            ILogger<ApexService> logger,
            IProviderService providerService,
            IRequestLogService requestLogService,
            IMapper mapper,
            IHttpContextAccessor contextAccessor,
            IOptionsMonitor<UrlSettings> urlOptions,
            IHttpRequestExecutorService httpRequestExecutorService, IConfiguration configuration, IRequestHeaders requestHeaders, IHttpClientWrapper httpClientWrapper, IApexToken apexToken)
        {
            this.logger = logger;
            this.httpClient = httpClient;
            this.providerService = providerService;
            this.requestLogService = requestLogService;
            this.httpClientWrapper = httpClientWrapper;
            this.mapper = mapper;
            this.contextAccessor = contextAccessor;
            this.urlOptions = urlOptions;
            this.httpRequestExecutorService = httpRequestExecutorService;
            var headerCorrelationId = contextAccessor.HttpContext?.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName];
            correlationId = headerCorrelationId.HasValue ? headerCorrelationId.Value.ToString() : Guid.Empty.ToString();
            this.configuration = configuration;
            this.apexToken = apexToken;
            this.requestHeaders = requestHeaders;
        }

        public async Task CreateProduct(ProductAction product, List<ProviderQueueConfigResponseModel> providerConfig, string accesstoken, Guid? correlationId, MerchantAction merchantPayload)
        {
            var validator = new ProductValidator();
            var result = validator.Validate(product);
            if (!result.IsValid)
            {
                foreach (var error in result.Errors)
                {
                    logger.LogError("Property: {PropertyName}, Error: {ErrorMessage}", error.PropertyName, error.ErrorMessage);
                }
                throw new ValidationException(result);
            }
            await CreateTerminal(providerConfig, product, accesstoken, correlationId, merchantPayload);
        }




        public async Task CreateTerminal(List<ProviderQueueConfigResponseModel> providerConfig, ProductAction? productPayload, string token, Guid? correlationId, MerchantAction merchantPayload)
        {
            // will go wrong check
            var apiUrl = providerConfig.Find(x => x.Action == AppConstants.Actions.Create && x.Entity == AppConstants.Entity.Product);

            var requestUri = $"{apiUrl!.APIBaseUrl}{apiUrl.TargetURL}";

            using (logger.BeginScope($"{nameof(CreateTerminal)}({requestUri})"))
            {
                logger.LogInformation("Calling Apex Service to create terminal Message");

                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new LowercaseContractResolver(),
                    Formatting = Formatting.Indented
                };

                var payload = JsonConvert.SerializeObject(productPayload, settings);
                HttpContent content = new StringContent(payload, Encoding.UTF8)
                {
                    Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
                };

                Dictionary<string, string>? headers = new() { { "Authorization", "Bearer " + token } };

                HttpResponseMessage response;
                string jsonResult = string.Empty;
                try
                {
                    logger.LogInformation("Calling APEX to create create terminal Request UrI: {@requestUri}", requestUri);
                    logger.LogInformation("Calling APEX to create create terminal Payload: {@payload}", payload);

                    response = await httpClientWrapper.SendHttpPostRequest(requestUri, content, headers);
                    jsonResult = await response.Content.ReadAsStringAsync();

                    if (string.IsNullOrEmpty(jsonResult))
                    {
                        logger.LogCritical("Response from APEX is empty. StatusCode: {StatusCode}", (int)response.StatusCode);
                        throw new PassthroughException(response);
                    }

                    var responseBody = JsonConvert.DeserializeObject<CreateOrderResponse>(jsonResult);

                    if (!response.IsSuccessStatusCode || (!string.IsNullOrEmpty(responseBody.ResponseCode)
                                                         && !responseBody.ResponseCode.Equals("00")
                                                         && !responseBody.ResponseCode.Equals("02")))
                    {
                        logger.LogCritical("Error when calling Apex Create terminal Message. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);
                        throw new PassthroughException(response);
                    }

                    logger.LogInformation("Successfull calling Apex to create Terminal Message. StatusCode: {StatusCode} Response: {@responseBody}", (int)response.StatusCode, jsonResult);

                    await requestLogService.CreateRequestLogAsync(CreateSuccessRequestLog(correlationId, productPayload, jsonResult));
                }
                catch (Exception ex)
                {
                    logger.LogCritical("Terminal/Product creation failed. Error: {Error}", ex.Message);

                    if (ex.Message.Contains(AppConstants.TokenError))
                    {
                        var newAccessToken = await apexToken.GetTokenAsync(providerConfig.FirstOrDefault()!, merchantPayload!, correlationId, productPayload!.MerchantId, AppConstants.Entity.Product);
                        // Retry the request with the new access token
                        await CreateProduct(productPayload!, providerConfig, newAccessToken.access_token!, correlationId, merchantPayload);
                    }
                    await requestLogService.CreateRequestLogAsync(CreateFailedRequestLog(correlationId, productPayload, payload, ex));

                    throw new FederationException(
                    level: "Order",
                    provider: "APEX",
                    message: $"Terminal creation failed. Error: {ex.Message}",
                    innerException: ex);
                }
            }
        }

        private RequestLog CreateFailedRequestLog(Guid? correlationId, ProductAction? productPayload, string payload, Exception ex)
        {
            return new RequestLog
            {
                CorrelationId = correlationId,
                ParentCorrelationId = correlationId,
                EntityId = productPayload!.MerchantId ?? string.Empty,
                Status = AppConstants.Status.Failed,
                Entity = AppConstants.Entity.Product,
                EntryDate = DateTime.UtcNow,
                RequestId = RequestIdHeader,
                RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(payload)),
                RequestType = AppConstants.Actions.Create,
                ResponseMetaData = ex.Message
            };
        }
        private RequestLog CreateSuccessRequestLog(Guid? correlationId, ProductAction? productPayload, string jsonResult)
        {
            return new RequestLog
            {
                CorrelationId = correlationId,
                ParentCorrelationId = correlationId,
                EntityId = productPayload!.MerchantId ?? string.Empty,
                Status = AppConstants.Status.Completed,
                Entity = AppConstants.Entity.Product,
                EntryDate = DateTime.UtcNow,
                RequestId = RequestIdHeader,
                RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(productPayload))),
                RequestType = AppConstants.Actions.Create,
                ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult))
            };
        }
    }
}