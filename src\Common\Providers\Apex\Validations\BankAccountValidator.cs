﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;
using System;

namespace Common.Providers.Apex.Validations;

public class BankAccountValidator : AbstractValidator<BankAccount>
{
    public BankAccountValidator()
    {
        RuleFor(x => x.IBAN)
            .NotEmpty()
            .WithErrorCode(Errors.IBANRequired.Code)
            .WithMessage(Errors.IBANRequired.Message);

    }
}