﻿using System;
using System.Collections.Generic;

namespace Common.Models;

public class ShareholderComapnyCheck
{
    public Guid Id { get; set; }

    public Guid MerchantId { get; set; }
    public Guid? MerchantShareholderCompanyId { get; set; }

    public string CheckType { get; set; } = string.Empty;

    public DateTime CheckDate { get; set; }

    public string CheckStatus { get; set; } = string.Empty;

    public int CheckScore { get; set; }

    public int CheckProvider { get; set; }

    public string? CheckTriggerType { get; set; }

    public bool? IsFinscanTriggerAutomatic { get; set; }

    public DateTime ValidFrom { get; set; }

    public DateTime? ValidTo { get; set; }

    public ICollection<CheckComment> CheckComments { get; set; } = new List<CheckComment>();

    public ICollection<ShareholderComapnyCheckPayload> ShareholderComapnyCheckPayloads { get; set; } = new List<ShareholderComapnyCheckPayload>();

    public string CreatedBy { get; set; } = string.Empty;

    public string? UpdatedBy { get; set; }
}
