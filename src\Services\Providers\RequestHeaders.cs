﻿using Common.Services;
using System;
using System.Net.Http;
using System.Net.Http.Headers;

namespace Services.Providers;

public class RequestHeaders : IRequestHeaders
{
    private readonly string XCorrelationIdHeader = "X-Correlation-ID";
    private readonly string RequestIdHeader = "X-Request-ID";
    private readonly string ApplicationLanguageHeader = "X-Application-Language";
    private readonly string CounterpartyCodeHeader = "X-Counterparty-Code";

    public void AddHeaders(HttpRequestMessage requestMessage, string accessToken, string correlationId)
    {
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        requestMessage.Headers.Add(XCorrelationIdHeader, correlationId);
        requestMessage.Headers.Add(RequestIdHeader, Guid.NewGuid().ToString());
        requestMessage.Headers.Add(ApplicationLanguageHeader, "en-US");
        requestMessage.Headers.Add(CounterpartyCodeHeader, "GEIDEA_UAE");
    }
}