﻿using Common.Models.Apex;
using Common.Models.Configuration;
using Geidea.Utils.Messaging.Base;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IApexTerminalClient
    {
        Task CreateProduct(ProductAction product, List<ProviderQueueConfigResponseModel> config, string accesstoken, Guid? correlationId, MerchantAction merchantAction);
       
    }
}