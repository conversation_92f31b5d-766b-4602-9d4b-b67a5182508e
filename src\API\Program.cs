using Geidea.PaymentGateway.ConfigServiceClient;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System;
using System.Runtime.InteropServices;

namespace API;

public static class Program
{
    // The workaround solution to meet integrated security auth for sql (https://github.com/dotnet/SqlClient/issues/1390), will be fixed with .net 6.0.2
    [DllImport("System.Net.Security.Native", EntryPoint = "NetSecurityNative_EnsureGssInitialized")]
    internal static extern int EnsureGssInitialized();

    public static void Main(string[] args)
    {
        try
        {
            Console.WriteLine("Normal app startup, Continue...");
            CreateHostBuilder(args).Build().Run();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());
            throw;
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
       Host.CreateDefaultBuilder(args)
           .ConfigureWebHostDefaults(webBuilder =>
           {
               webBuilder.UseStartup<Startup>();
               if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("IsRunInEnvironments")))
               {
                   webBuilder.UseConfigService();
               }
           });
}