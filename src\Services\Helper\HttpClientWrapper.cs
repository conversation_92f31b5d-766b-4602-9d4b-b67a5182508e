﻿using Microsoft.Extensions.Logging;
using Services.Helper.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Mime;
using System.Text.Json;
using System.Text.Json.Serialization;
using Common.Options;
using Microsoft.Extensions.Configuration;
using System.Net.Security;
using Polly;
using Common.Models.Apex;
using Newtonsoft.Json;

namespace Services.Helper;
[ExcludeFromCodeCoverage]
public class HttpClientWrapper : IHttpClientWrapper
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<HttpClientWrapper> _logger;

    public HttpClientWrapper(HttpClient httpClient, ILogger<HttpClientWrapper> logger, IConfiguration configuration, bool test = false)
    {
        var apexSettings = configuration.GetSection("Apex").Get<ApexSettings>();
        var httpClientHandler = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) =>
            {
                if (apexSettings.IgnoreApexSSLValidation)
                {
                    return true;
                }
                else
                {
                    if (sslPolicyErrors == SslPolicyErrors.None)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
        };

        _httpClient = test ? httpClient : new HttpClient(httpClientHandler);
        _logger = logger;
    }

    public async Task<HttpResponseMessage> SendHttpGetRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        _logger.LogInformation("[SendHttpGetRequest] Start sending Get request to {requestUri}", requestUri);
        var response = await SendHttpRequest(HttpMethod.Get, requestUri, httpContent, headers: headers);
        CheckResponse(requestUri, response);

        return response;
    }

    public async Task<HttpResponseMessage> SendHttpPostRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        _logger.LogInformation("[SendHttpPostRequest] Start sending Post request to {requestUri}", requestUri);
        
        var retryPolicy = Policy.Handle<TaskCanceledException>().Or<HttpRequestException>()
            .OrResult<HttpResponseMessage>(response => !response.IsSuccessStatusCode)
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                                onRetry: (outcome, timespan, retryCount, context) =>
                                {
                                    _logger.LogInformation("Retry {RetryCount} at {TimeSpan} due to {Outcome}", retryCount, timespan, outcome.Exception?.Message);
                                });

        return await retryPolicy.ExecuteAsync(async () =>
        {
            var response = await SendHttpRequest(HttpMethod.Post, requestUri, httpContent, headers);
            CheckResponse(requestUri, response);
            return response;
        });
    }

    public async Task<HttpResponseMessage> SendHttpPostRequestForGetMerchant(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        _logger.LogInformation("[SendHttpPostRequest] Start sending Post request to {requestUri}", requestUri);
        
        var retryPolicy = Policy.Handle<TaskCanceledException>().Or<HttpRequestException>()
            .OrResult<HttpResponseMessage>(response => !response.IsSuccessStatusCode || IsMerchantNull(response))
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                _logger.LogInformation("Retry {RetryCount} at {TimeSpan} due to {Outcome}", retryCount, timespan, outcome.Exception?.Message);
            });

        return await retryPolicy.ExecuteAsync(async () =>
        {
            var response = await SendHttpRequest(HttpMethod.Post, requestUri, httpContent, headers);
            CheckResponse(requestUri, response);
            return response;
        });
    }

    public async Task<HttpResponseMessage> SendHttpPutRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        _logger.LogInformation("[SendHttpPutRequest] Start sending Put request to {requestUri}", requestUri);
        var response = await SendHttpRequest(HttpMethod.Put, requestUri, httpContent, headers);
        CheckResponse(requestUri, response);

        return response;
    }

    public async Task<HttpResponseMessage> SendHttpPatchRequest(string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        _logger.LogInformation("[SendHttpPatchRequest] Start sending Patch request to {requestUri}", requestUri);

        var retryPolicy = Policy.Handle<TaskCanceledException>().Or<HttpRequestException>()
            .OrResult<HttpResponseMessage>(response => !response.IsSuccessStatusCode)
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                _logger.LogInformation("Retry {RetryCount} at {TimeSpan} due to {Outcome}", retryCount, timespan, outcome.Exception?.Message);
            });
        
        return await retryPolicy.ExecuteAsync(async () =>
        {
            var response = await SendHttpRequest(HttpMethod.Patch, requestUri, httpContent, headers);
            CheckResponse(requestUri, response);
            return response;
        });
    }

    private void CheckResponse(string requestUri, HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
        {
            var jsonResult = response.Content.ReadAsStringAsync().Result;
            _logger.LogError("[CheckResponse] Request to {@requestUri} failed with {@statusCode}", requestUri, response.StatusCode);
            throw new HttpRequestException(jsonResult, new Exception(), response.StatusCode);
        }
    }

    private static bool IsMerchantNull(HttpResponseMessage response)
    {
        var responseContent = response.Content.ReadAsStringAsync().Result;
        var data = JsonConvert.DeserializeObject<MerchantAction>(responseContent);
        return data?.Merchant == null;
    }

    private async Task<HttpResponseMessage> SendHttpRequest(HttpMethod method, string requestUri, HttpContent httpContent, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(method, requestUri);

        if (headers != null)
        {
            foreach (KeyValuePair<string, string> header in headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }
        }

        if (httpContent != null)
            request.Content = httpContent;

        return await _httpClient.SendAsync(request);
    }
}
