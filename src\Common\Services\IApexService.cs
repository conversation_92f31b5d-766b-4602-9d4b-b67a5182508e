﻿using Common.Models.Apex;
using Common.Models.Configuration;
using Geidea.Utils.Messaging.Base;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IApexService
    {
        Task CreateMerchant(GenericMessage<Common.Models.Merchant> merchant, List<ProviderQueueConfigResponseModel> config);
        Task CreateOrder(GenericMessage<Common.Models.Merchant> merchant, List<ProviderQueueConfigResponseModel> config);
    }
}