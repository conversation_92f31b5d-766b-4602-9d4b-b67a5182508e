﻿using System;

namespace Common.Models.Configuration;

public class ProviderPropertyConfig
{
    public string InternalField { get; set; } = string.Empty;
    public string ExternalField { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public int? MaxLength { get; set; }
    public bool? Required { get; set; }
    public string Regex { get; set; } = string.Empty;
    public bool? IsActive { get; set; }
    public string Entity { get; set; } = string.Empty;
}