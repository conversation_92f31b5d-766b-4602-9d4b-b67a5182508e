﻿namespace Common.Models;

public class MerchantExternalProduct
{
    public string? ProductCode { get; set; }
    public string? Description { get; set; }
    public string? PaymentMethod { get; set; }
    public string? ChargeFrequency { get; set; }
    public string? ProductCategory { get; set; }
    public string? ChargeType { get; set; }
    public string? ProductType { get; set; }
    public string? ProductGroup { get; set; }
    public string? CounterParty { get; set; }
    public int? DisplayOrder { get; set; }
    public bool? IsProofRequired { get; set; }
}