﻿using Common.Constants;
using Common.Models;
using Common.Models.Configuration;
using Common.Services;
using Geidea.Utils.Messaging.Base;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Services.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Services.Orchestration;

public class OrderCreate : IOrderCreate
{
    private readonly ILogger<MerchantCreate> logger;
    private readonly IApexService apexservice;
    private readonly IApexTerminalClient apexTerminalClient;

    private readonly IServiceScopeFactory serviceScopeFactory;
    private readonly IProviderService providerService;

    public OrderCreate(ILogger<MerchantCreate> logger, IApexService apexService, IProviderService providerService, IServiceScopeFactory serviceScopeFactory, IApexTerminalClient apexTerminalClient)
    {
        this.logger = logger;
        this.apexservice = apexService;
        this.providerService = providerService;
        this.serviceScopeFactory = serviceScopeFactory;
        this.apexTerminalClient = apexTerminalClient;
    }

    public async Task OrderMessageCreator(GenericMessage<Merchant> merchant)
    {
        try
        {
            var providers = await providerService.GetProviderQueueConfiguration(new QueueConfigSearchFilter { CounterParty = merchant.Header.Counterparty! });
            //fetch apex data
            var apexData = providers.Where(x => x.ProviderCode == AppConstants.Providers.Apex).ToList();
            using var scope = serviceScopeFactory.CreateScope();

            var apexService = scope.ServiceProvider.GetRequiredService<IApexService>();

            Task.Run(() => apexService.CreateOrder(merchant, apexData))
                                               .Wait();
            logger.LogInformation("Order federation completed successfully");
        }
        catch (Exception exception)
        {
            logger.LogError("Message failed to process. Error: {ErrorMessage}", exception.Message);
            throw;
        }
    }
}