﻿using Common.Constants;
using Common.Models;
using Common.Models.Apex;
using Common.Models.Configuration;
using Common.Providers.Apex.Validations;
using Common.Services;
using Geidea.Utils.Common;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;
using static Common.Constants.AppConstants;
using Common.Exceptions;

namespace Services.Providers.Apex
{
    public class ApexService : IApexService
    {
        private readonly ILogger<ApexService> logger;
        private readonly IApexTerminalClient terminalClient;
        private readonly IApexBusinessAccountClient businessClient;
        private readonly IApexGetMerchantClient getClient;
        private readonly IApexToken token;

        private static readonly SemaphoreSlim semaphore = new(1, 1);

        public ApexService(ILogger<ApexService> logger, IApexTerminalClient terminalClient,
            IApexBusinessAccountClient businessAccountClient, IApexGetMerchantClient getMerchantClient,
            IApexToken token)
        {
            this.logger = logger;
            this.terminalClient = terminalClient;
            this.businessClient = businessAccountClient;
            this.getClient = getMerchantClient;
            this.token = token;
        }

        // This checks if the account is already present: if not, creates a new account else, creates terminal for existing account.
        public async Task CreateOrder(GenericMessage<Common.Models.Merchant> merchant, List<ProviderQueueConfigResponseModel> config)
        {
            try
            {
                logger.LogInformation("Start creating apex order data.");

                var validator = new MerchantValidator();
                var result = validator.Validate(merchant.Data);
                if (!result.IsValid)
                {
                    foreach (var error in result.Errors)
                    {
                        logger.LogError("Property: {PropertyName}, Error: {ErrorMessage}", error.PropertyName, error.ErrorMessage);
                    }
                    throw new ValidationException(result);
                }

                var merchantData = merchant.Data;
                var merchantPayload = MapToApexMerchant(merchantData, merchantData.Counterparty!);

                var apexTerminals = merchantData.MerchantTerminals.Select(terminal => new Common.Models.Apex.MerchantTerminal
                {
                    Brand = terminal.Brand,
                    SerialNumber = !string.IsNullOrEmpty(terminal.SerialNumber) ? terminal.SerialNumber : terminal.TerminalId,
                    MerchantId = !string.IsNullOrEmpty(terminal.MerchantId) ? terminal.MerchantId : string.Empty,
                    Model = terminal.Model,
                    TerminalId = merchantData.Counterparty == CounterParty.KSA ? terminal.FullTerminalId : terminal.TerminalId,
                    TerminalStatus = terminal.TerminalStatus ?? AppConstants.ReadyToAssign
                }).ToList();

                var apexCommissionList = MapApexCommissionProducts(merchantData.CommissionTypes!.ToList(), merchant.Data);
                var dcc = MapApexDcc(merchantData.CommissionTypes!.ToList(), merchantData.Counterparty!);

                var responseData = await token.GetTokenAsync(config.FirstOrDefault()!, merchantPayload!, merchant.Header.CorrelationId, merchant.Data.MerchantDetails.MID, Entity.Order);

                if (responseData != null && !string.IsNullOrEmpty(responseData.access_token))
                {
                    merchantPayload!.Merchant.MerchantId = merchantData.MerchantDetails?.MID;
                    merchantPayload!.Merchant.ParentMerchantId = merchantData.MerchantDetails?.BusinessId;
                    merchantPayload!.Merchant.MerchantLevel = MerchantTypes.AccountLevel;
                    merchantPayload!.Action = MerchantCreateAction.Add;

                    var apiUrl = config.Find(x => x.Action == Actions.Get);
                    var requestGetUri = $"{apiUrl!.APIBaseUrl}{apiUrl.TargetURL}";
                    bool isExist = await getClient.CheckIfExists(merchantPayload, responseData.access_token!, requestGetUri, merchant, config.FirstOrDefault()!);

                    if (isExist)
                    {
                        merchantPayload!.Merchant.LinkedTerminals = apexTerminals;
                        var productPayload = MapToApexProduct(merchantPayload);
                        await terminalClient.CreateProduct(productPayload!, config, responseData.access_token, merchant.Header.CorrelationId, merchantPayload);
                    }
                    else
                    {
                        merchantPayload.Merchant.LinkedTerminals = apexTerminals;
                        merchantPayload.Merchant.Commission = apexCommissionList;
                        merchantPayload.Merchant.DccSet = dcc;

                        if (merchantData.Counterparty == CounterParty.KSA)
                        {
                            MapToApexBankDetails(merchantData, merchantPayload);
                        }

                        logger.LogInformation("Sending APEX request to create Account as it is not exist");
                        await businessClient.CreateBusinessOrAccount(merchant, config, merchantPayload, responseData!);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Error while creating order {@ex}", ex.ToString());
                throw;
            }
        }

        // This creates a business first, then with additional info creates single or multiple account.
        public async Task CreateMerchant(GenericMessage<Common.Models.Merchant> merchant, List<ProviderQueueConfigResponseModel> config)
        {
            try
            {
                logger.LogInformation("Start creating apex merchant data.");
                var merchantData = merchant.Data;
                var validator = new MerchantValidator();

                var result = validator.Validate(merchantData);
                if (!result.IsValid)
                {
                    foreach (var error in result.Errors)
                    {
                        logger.LogError("Property: {PropertyName}, Error: {ErrorMessage}", error.PropertyName, error.ErrorMessage);
                    }

                    throw new ValidationException(validationResult: result);
                }

                var merchantPayload = MapToApexMerchant(merchantData, merchantData.Counterparty!);
                var responseData = await token.GetTokenAsync(config.FirstOrDefault()!, merchantPayload!, merchant.Header.CorrelationId, merchant.Data.MerchantDetails.MID, Entity.Merchant);

                if (responseData != null && !string.IsNullOrEmpty(responseData.access_token))
                {
                    try
                    {
                        await semaphore.WaitAsync();
                        var apiUrl = config.Find(x => x.Action == Actions.Get);
                        var requestGetUri = $"{apiUrl!.APIBaseUrl}{apiUrl.TargetURL}";
                        bool isExist = await getClient.CheckIfExists(merchantPayload!, responseData.access_token!, requestGetUri, merchant, config.FirstOrDefault()!);

                        if (!isExist)
                        {
                            try
                            {
                                //Create business
                                logger.LogInformation("Sending APEX request to create business");
                                await businessClient.CreateBusinessOrAccount(merchant, config, merchantPayload, responseData!);
                            }
                            catch (Exception ex)
                            {
                                throw new FederationException(
                                    level: merchantPayload?.Merchant?.MerchantLevel == MerchantTypes.AccountLevel ? "Account" : "Business",
                                    provider: "APEX",
                                    message: "Error while creating business or account in APEX.",
                                    innerException: ex
                                  );
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }

                    // Create account
                    var apexCommissionList = MapApexCommissionProducts(merchantData.CommissionTypes!.ToList(), merchant.Data);
                    var apexFees = MapApexFees(merchantData.MerchantFees.ToList());
                    var dcc = MapApexDcc(merchantData.CommissionTypes!.ToList(), merchantData.Counterparty!);

                    if (checkMerchantTerminals(merchant.Data.MerchantTerminals.ToList()))
                    {
                        var apexTerminals = merchantData.MerchantTerminals.Select(terminal => new Common.Models.Apex.MerchantTerminal
                        {
                            Brand = terminal.Brand,
                            SerialNumber = !string.IsNullOrEmpty(terminal.SerialNumber) ? terminal.SerialNumber : terminal.TerminalId,
                            MerchantId = !string.IsNullOrEmpty(terminal.MerchantId) ? terminal.MerchantId : string.Empty,
                            Model = terminal.Model,
                            TerminalId = merchantData.Counterparty == CounterParty.KSA ? terminal.FullTerminalId : terminal.TerminalId,
                            TerminalStatus = terminal.TerminalStatus ?? AppConstants.ReadyToAssign
                        }).ToList();

                        merchantPayload!.Merchant.LinkedTerminals = apexTerminals;
                    }
                    else
                        merchantPayload!.Merchant.LinkedTerminals = new List<Common.Models.Apex.MerchantTerminal>();

                    merchantPayload!.Merchant.MerchantId = merchantData.MerchantDetails?.MID;
                    merchantPayload!.Merchant.ParentMerchantId = merchantData.MerchantDetails?.BusinessId;
                    merchantPayload!.Merchant.MerchantLevel = MerchantTypes.AccountLevel;
                    merchantPayload!.Action = MerchantCreateAction.Add;
                    merchantPayload.Merchant.Commission = apexCommissionList;
                    merchantPayload.Merchant.Fee = apexFees;
                    merchantPayload.Merchant.DccSet = dcc;

                    if (merchantData.Counterparty == CounterParty.KSA)
                    {
                        MapToApexBankDetails(merchantData, merchantPayload);
                    }

                    try
                    {
                        //Create Account
                        logger.LogInformation("Sending APEX request to create Account");
                        await businessClient.CreateBusinessOrAccount(merchant, config, merchantPayload, responseData!);
                    }
                    catch (Exception ex)
                    {
                        throw new FederationException(
                            level: merchantPayload?.Merchant?.MerchantLevel == MerchantTypes.AccountLevel ? "Account" : "Business",
                            provider: "APEX",
                            message: "Error while creating business or account in APEX.",
                            innerException: ex
                          );
                    }
                    // call terminals or order after completing the above processes.
                    if (merchantData.Counterparty == CounterParty.KSA && merchant.Data.MerchantTerminals.Any())
                    {
                        if (checkMerchantTerminals(merchant.Data.MerchantTerminals.ToList()))
                        {
                            try
                            {
                                await CreateOrder(merchant, config);

                            }
                            catch (Exception ex)
                            {
                                logger.LogError("Order Level creation failed: {Error}", ex.Message);
                                throw new Exception($"Order Level creation failed: {ex.Message}", ex);
                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                logger.LogError("Error while creating merchant {@ex}", ex.ToString());
                throw;
            }
        }

        private bool checkMerchantTerminals(List<Common.Models.MerchantTerminal> terminals)
        {

            if (terminals.Count == 1 && terminals[0].TerminalStatus == "Not Assigned" &&
                terminals[0].TerminalId == null &&
                terminals[0].FullTerminalId == null &&
                terminals[0].Brand == null &&
                terminals[0].Model == null)
            {
                return false;
            }
            else
            {
                var validator = new MerchantTerminalValidator();

                foreach (var terminal in terminals)
                {
                    var validationResult = validator.Validate(terminal);
                    if (!validationResult.IsValid)
                    {
                        foreach (var error in validationResult.Errors)
                        {
                            logger.LogError("Property: {PropertyName}, Error: {ErrorMessage}", error.PropertyName, error.ErrorMessage);
                        }
                        throw new ValidationException(validationResult: validationResult);
                    }
                }
                return true;
            }
        }

        private List<MerchantDccSet> MapApexDcc(List<MerchantCommissionConfig> dcc, string counterParty)
        {
            var dccSet = new List<MerchantDccSet>();
            if (dcc!.Count <= 0)
            {
                return dccSet;
            }
            var checkIfVi = dcc.Find(x => x.ProductCode == VCI);
            var checkIfMi = dcc.Find(x => x.ProductCode == MCI);

            if (checkIfVi != null)
            {
                dccSet.Add(new MerchantDccSet()
                {
                    CommissionType = typeP,
                    TransactionType = TT,
                    PercentageAmount = checkIfVi.Value.ToString(),
                    FlatAmount = Zero,
                    ProductCode = VI,
                    MinCommAmount = Zero,
                    MaxCommAmount = AppConstants.MaxValue,
                    ValidFrom = DateTime.UtcNow.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                    ValidTo = AppConstants.MaxExpiryDate,
                    VatPercentage = counterParty == CounterParty.KSA ? AppConstants.Vat : checkIfVi.VatPercentage ?? 0,
                    IsInterchange = Zero,
                    PremiumFlatAmount = Zero,
                    PremiumPercentageAmount = checkIfVi.Value.ToString(),

                });
            }
            if (checkIfMi != null)
            {
                dccSet.Add(new MerchantDccSet()
                {
                    CommissionType = typeP,
                    TransactionType = TT,
                    PercentageAmount = checkIfMi.Value.ToString(),
                    FlatAmount = Zero,
                    ProductCode = MI,
                    MinCommAmount = Zero,
                    MaxCommAmount = AppConstants.MaxValue,
                    ValidFrom = DateTime.UtcNow.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                    ValidTo = AppConstants.MaxExpiryDate,
                    VatPercentage = counterParty == AppConstants.CounterParty.KSA ? AppConstants.Vat : checkIfMi.VatPercentage ?? 0,
                    IsInterchange = Zero,
                    PremiumFlatAmount = Zero,
                    PremiumPercentageAmount = checkIfMi.Value.ToString(),
                });
            }
            return dccSet;
        }

        private static ProductAction? MapToApexProduct(MerchantAction product)
        {
            var productAction = new ProductAction();
            if (product == null)
            {
                return null;
            }
            var terminal = product.Merchant.LinkedTerminals;
            if (terminal != null)
            {
                var merchantTerminals = terminal.Select(t => new Common.Models.Apex.MerchantTerminal
                {
                    Brand = t.Brand,
                    SerialNumber = !string.IsNullOrEmpty(t.SerialNumber) ? t.SerialNumber : t.TerminalId,
                    MerchantId = !string.IsNullOrEmpty(t.MerchantId) ? t.MerchantId : string.Empty,
                    Model = t.Model,
                    TerminalId = t.TerminalId,
                    TerminalStatus = t.TerminalStatus ?? AppConstants.ReadyToAssign
                }).ToList();

                productAction = new ProductAction
                {
                    Action = AppConstants.MerchantCreateAction.Add,
                    MerchantId = product.Merchant.MerchantId,
                    Terminal = merchantTerminals,
                };
            }

            return productAction;
        }

        private static void MapToApexBankDetails(Common.Models.Merchant merchant, MerchantAction merchantAction)
        {
            var firstBankAccount = merchant.BankAccounts.FirstOrDefault();
            var accountConfig = merchant.AccountConfig;
            var counterParty = merchant.Counterparty!;

            var economicActivity = GetEconomicActivityValue(merchant.MerchantDetails.AcquiringLedger!, counterParty);

            merchantAction.Merchant.EconomicActivity = economicActivity;
            merchantAction.Merchant.Iban = firstBankAccount?.IBAN!;
            merchantAction.Merchant.BeneficiaryFullName = GetSubstring(firstBankAccount?.AccountHolderName!, 0, 256) ?? string.Empty;
            merchantAction.Merchant.PayoutSchedule = string.IsNullOrEmpty(accountConfig.PayoutSchedule!) && counterParty == CounterParty.KSA ? Offset : accountConfig.PayoutSchedule!;
            merchantAction.Merchant.SettlementCurrency = GetSettlementCurrencyValue(accountConfig.SettlementCurrency!, counterParty);
            merchantAction.Merchant.PaymentMethod = AppConstants.Payment;
            merchantAction.Merchant.PayoutBeneficiaryBank = firstBankAccount?.Swift!;
            merchantAction.Merchant.PayoutSourceBank = GetPayoutSourceBankValue(merchant.MerchantDetails.AcquiringLedger!, counterParty);
        }

        private MerchantAction? MapToApexMerchant(Common.Models.Merchant merchant, string counterParty)
        {
            logger.LogInformation("Start mapping merchant data to Apex merchant.");

            var merchantAction = new MerchantAction();

            if (merchant == null)
            {
                logger.LogError("merchant data is null.");
                return null;
            }

            try
            {
                var firstBankAccount = merchant.BankAccounts.FirstOrDefault();
                var firstPersonOfInterest = merchant.PersonOfInterests.FirstOrDefault();
                var firstAddress = merchant.Addresses.FirstOrDefault();
                var accountConfig = merchant.AccountConfig;
                var apexBusinessAddress = new Common.Models.Apex.Address();
                var cleanedStreet = RemoveSpecialCharacters(firstAddress?.Street ?? string.Empty);
                var cleanedContactStreet = RemoveSpecialCharacters(firstPersonOfInterest?.Address?.Street ?? string.Empty);
                var cleanedContactArea = RemoveSpecialCharacters(firstPersonOfInterest?.Address?.Area ?? string.Empty);

                string expectedMonthly = GetSubstring(merchant.MerchantDetails.MaxMonthlyTransaction.ToString(), 0, 10);
                int finalMonthlyValue = !string.IsNullOrEmpty(expectedMonthly) ? int.Parse(expectedMonthly) : 0;

                var apexContact = new Contact
                {
                    FirstName = firstPersonOfInterest?.FirstName,
                    LastName = firstPersonOfInterest?.LastName,
                    MobileNo = merchant.MerchantDetails?.CompanyPhoneNumber,
                    EmailAddress = firstAddress?.Email,
                    DateOfBirth = firstPersonOfInterest?.DateOfBirth,
                    IdNumber = firstPersonOfInterest?.NationalId,
                    PhoneNumber = firstPersonOfInterest?.PhoneNumber,
                    Fax = firstPersonOfInterest?.Fax,
                    ContactType = AppConstants.Zero,
                    Address = new Common.Models.Apex.Address
                    {
                        Line1 = cleanedContactStreet.Length > 64 ? GetSubstring(cleanedContactStreet, 0, 64) : cleanedContactStreet,
                        Line2 = MapLine2BasedOnCounterparty(cleanedContactStreet, cleanedContactArea, counterParty),
                        City = firstPersonOfInterest?.Address?.City,
                        Country = firstPersonOfInterest?.Address?.Country,
                        District = firstPersonOfInterest?.Address?.Governorate,
                        ZipCode = firstPersonOfInterest?.Address?.Zip,
                        PhoneNumber = firstPersonOfInterest?.Address?.PhoneNumber,
                    }
                };

                if (string.IsNullOrEmpty(apexContact.Address.Line1))
                {
                    apexBusinessAddress.Line1 = "No Address Defined";
                }

                apexBusinessAddress = new Common.Models.Apex.Address
                {
                    Line1 = cleanedStreet.Length > 64 ? GetSubstring(cleanedStreet, 0, 64) : cleanedStreet,
                    Line2 = MapLine2Address(cleanedStreet),
                    Country = firstAddress?.Country,
                    City = firstAddress?.City,
                    Province = counterParty == CounterParty.KSA ? firstAddress?.City : string.Empty,
                    District = counterParty == CounterParty.KSA ? firstAddress?.Area : firstAddress?.Governorate,
                    ZipCode = AppConstants.Zip,
                    PhoneNumber = merchant.MerchantDetails?.CompanyPhoneNumber,
                    AddressType = Zero,
                    EmailAddress = firstAddress?.Email,
                };

                if (string.IsNullOrEmpty(apexBusinessAddress.Line1))
                {
                    apexBusinessAddress.Line1 = "No Address Defined";
                }

                var finalPaymentMethod = string.Join(",", merchant.CommissionTypes.Select(commissionType => commissionType.ProductCode));

                var merchantMapData = new Common.Models.Apex.Merchant
                {
                    MerchantId = merchant.MerchantDetails?.BusinessId,
                    MerchantLevel = GetMerChantLevel(merchant.MerchantType),
                    ParentMerchantId = null,
                    StoreName = GetSubstring(merchant.MerchantDetails?.AlternativeMerchantName!, 0, 256),
                    MerchantSegment = merchant.MerchantDetails?.MerchantSegment,
                    RiskLevel = merchant.MerchantDetails!.RiskLevel,
                    CommercialRegistrationNumber = GetCommercialRegistrationNumber(merchant.MerchantDetails?.RegistrationNumber, merchant.MerchantDetails?.UnifiedId, counterParty),
                    AlternativeStoreName = GetSubstring(merchant.MerchantDetails?.AlternativeMerchantName!, 0, 256),
                    Mcc = merchant.MerchantDetails?.MCC,
                    Status = merchant.MerchantStatus,
                    ContractExpiryDate = AppConstants.MaxExpiryDate.Replace(".", ""),
                    ContractStartDate = DateTime.UtcNow.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                    AlternativeMerchantName = GetSubstring(merchant.MerchantDetails?.LegalName!, 0, 256),
                    MerchantName = GetSubstring(merchant.MerchantDetails?.LegalName!, 0, 256),
                    TradeLicenseExpiryDate = merchant!.MerchantDetails!.TLExpiryDate?.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                    TradeLicenseIssueDate = merchant!.MerchantDetails!.TLIssueDate?.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                    Iban = firstBankAccount?.IBAN!,
                    BeneficiaryFullName = GetSubstring(firstBankAccount?.AccountHolderName!, 0, 256) ?? string.Empty,
                    AccountNumber = firstBankAccount?.BankAccountNumber!,
                    BIC = firstBankAccount?.Swift!,
                    PayoutSchedule = string.IsNullOrEmpty(accountConfig.PayoutSchedule!) && counterParty == CounterParty.KSA ? Offset : accountConfig.PayoutSchedule!,
                    PayoutOffset = accountConfig.PayoutSchedule == Offset ? (accountConfig.SettlementTimeFrame == 1 ? 0 : accountConfig.SettlementTimeFrame) : accountConfig.PayoutDay,
                    PayoutCapAmount = accountConfig.PayoutCapAmount.ToString(),
                    PayoutMinimumCap = accountConfig.PayoutMinimumCap.ToString(),
                    PayoutTransferFeeAmount = accountConfig.PayoutTransferFeeAmount.ToString(),
                    SettlementCurrency = GetSettlementCurrencyValue(accountConfig.SettlementCurrency!, counterParty),
                    ExpectedHighestTransactionAmount = merchant.MerchantDetails.HighestSingleTransaction,
                    ExpectedMonthlyVolume = finalMonthlyValue,
                    VatNumber = GetSubstring(merchant.MerchantDetails.VatNumber!, 0, 20) ?? string.Empty,
                    ClientNumber = merchant.MerchantDetails.ClientNumber ?? string.Empty,
                    UnifiedNumber = merchant.MerchantDetails.UnifiedId ?? string.Empty,
                    Contact = apexContact,
                    Address = apexBusinessAddress,
                    Products = ApexRequestTransformer.ProductMapper.GetApexProductsCommission(finalPaymentMethod!.Split(',')),
                    PaymentMethod = AppConstants.Payment,
                    PayoutBeneficiaryBank = firstBankAccount?.Swift!,
                    PayoutSourceBank = counterParty == CounterParty.UAE ? AppConstants.BankName : null!,
                    PayoutSourceBankType = counterParty == CounterParty.UAE ? AppConstants.BankType : null!,
                    TerminalOwnerId = merchant.MerchantDetails.ReferralChannel == ChannelType.HSBC ? 1 : 3, // 1 : HSBC , 3: GEIDEA/Unassigned
                };


                if (counterParty == Constants.CounterpartySaudi)
                {
                    string? settleValue = merchant.CommissionTypes.FirstOrDefault(x => x.ProductCode == AppConstants.SettleValue)?.Value.ToString();
                    merchantMapData.AlternativeMerchantName = !string.IsNullOrEmpty(merchant.MerchantDetails.LegalNameAr) ? merchant.MerchantDetails.LegalNameAr : merchantMapData.AlternativeMerchantName;
                    merchantMapData.AlternativeStoreName = !string.IsNullOrEmpty(merchant.MerchantDetails.LegalNameAr) ? merchant.MerchantDetails.LegalNameAr : merchantMapData.AlternativeStoreName;
                    merchantMapData.StoreName = merchant.MerchantDetails.LegalName;
                    merchantMapData.TerminalOwnerId = null;
                    merchantMapData.Address.Country = AppConstants.Saudi;
                    merchantMapData.PayoutTransferFeeAmount = !string.IsNullOrEmpty(settleValue) ? settleValue : Zero;
                    merchantMapData.Contact.Address = null;
                    merchantMapData.BusinessType = merchant.MerchantDetails.BusinessType;
                }

                merchantAction = new MerchantAction
                {
                    Action = MerchantCreateAction.Add,
                    Merchant = merchantMapData
                };

                merchantAction = ApexRequestTransformer.GetMerchant(merchant, merchantAction);

                return merchantAction;
            }
            catch (Exception ex)
            {
                logger.LogError("Error while MapToApexMerchant {@Message}", ex.Message.ToString());
                throw new ServiceException(HttpStatusCode.InternalServerError, ex.Message.ToString());
            }
        }

        private static string MapLine2BasedOnCounterparty(string? cleanedStreet, string? cleanedArea, string counterparty)
        {
            if (counterparty == CounterParty.UAE)
            {
                return MapLine2Address(cleanedStreet);
            }

            return !string.IsNullOrEmpty(cleanedArea)
                ? cleanedArea.Length > 64 ? GetSubstring(cleanedArea, 0, 64) : cleanedArea
                : string.Empty;
        }

        private static string MapLine2Address(string? cleanedStreet)
        {
            if (!string.IsNullOrEmpty(cleanedStreet))
            {
                int line1Length = Math.Min(64, cleanedStreet.Length);

                return cleanedStreet.Length > line1Length
                    ? cleanedStreet.Substring(line1Length, Math.Min(64, cleanedStreet.Length - line1Length))
                    : string.Empty;
            }
            return string.Empty;
        }

        private static string GetMerChantLevel(string? merchantType)
        {
            switch (merchantType)
            {
                case MerchantTypes.Chain:
                    {
                        return ApexRequestTransformer.OrganizationLevel.Chain;
                    }
                case MerchantTypes.Business:
                    {
                        return ApexRequestTransformer.OrganizationLevel.Business;
                    }
                case MerchantTypes.Account:
                    {
                        return ApexRequestTransformer.OrganizationLevel.Account;
                    }
                default:
                    {
                        return "0";
                    }
            }
        }

        private static List<Commission> MapApexCommissionProducts(List<MerchantCommissionConfig>? merchantCommissionList, Common.Models.Merchant merchant)
        {
            var mappedList = new List<Commission>();
            if (merchantCommissionList!.Count <= 0)
            {
                return mappedList;
            }

            string? fixValue = merchantCommissionList.Find(x => x.ProductCode == AppConstants.FIX)?.Value.ToString();
            string? vcpValue = merchantCommissionList.Find(x => x.ProductCode == "VCP")?.Value.ToString();
            string? mcpValue = merchantCommissionList.Find(x => x.ProductCode == "MCP")?.Value.ToString();

            foreach (var item in merchantCommissionList)
            {
                var products = ApexRequestTransformer.ProductMapper.GetApexProducts(item.ProductCode!);
                if (products != null)
                {
                    foreach (var apexProduct in products)
                    {
                        //Card Not Present: Commission Type should have P + F, where F is Additional Fixed Fee per Transaction (ECOM) for all the card scheme from MA.

                        var isECOMorCNP = merchant.MerchantDetails.MerchantSegment != null &&
                                            (merchant.MerchantDetails.MerchantSegment.Contains(ChannelType.ECOM) ||
                                             merchant.MerchantDetails.MerchantSegment.Contains(ChannelType.PGW) ||
                                             merchant.MerchantDetails.MerchantSegment.Contains(ChannelType.CNP));

                        if (isECOMorCNP)
                        {
                            // Check if the merchant is in KSA and if the fixValue is empty or Zero
                            if (merchant.Counterparty == CounterParty.KSA &&
                                merchant.MerchantDetails.MerchantSegment!.Contains(ChannelType.ECOM) &&
                                (string.IsNullOrEmpty(fixValue) || fixValue == Zero))
                            {
                                // Add commission with typeP
                                AddCommissionToMappedList(typeP, Zero, Zero, item.Value.ToString(), apexProduct, item, merchant.Counterparty, vcpValue!, mcpValue!, mappedList);
                            }
                            else if (apexProduct == "P1")
                            {
                                AddCommissionToMappedList(typeP, Zero, Zero, item.Value.ToString(), apexProduct, item, merchant.Counterparty!, vcpValue!, mcpValue!, mappedList);
                            }
                            else
                            {
                                // Add commission with typeB
                                AddCommissionToMappedList(typeB, fixValue!, fixValue!, item.Value.ToString(), apexProduct, item, merchant.Counterparty!, vcpValue!, mcpValue!, mappedList);
                            }
                        }
                        else
                        {
                            // POS or CP
                            bool isValidType = (apexProduct == "VL" || apexProduct == "VI" || apexProduct == "ML" || apexProduct == "MI") && (!string.IsNullOrEmpty(fixValue) && fixValue != Zero);
                            if (isValidType)
                            {
                                AddCommissionToMappedList(typeB, fixValue!, fixValue!, item.Value.ToString(), apexProduct, item, merchant.Counterparty!, vcpValue!, mcpValue!, mappedList);
                            }
                            else
                            {
                                AddCommissionToMappedList(typeP, Zero, Zero, item.Value.ToString(), apexProduct, item, merchant.Counterparty!, vcpValue!, mcpValue!, mappedList);
                            }
                        }
                    }
                }
            }

            return mappedList;
        }

        private static void AddCommissionToMappedList(string commissionType, string flatAmount, string premiumFlatAmount, string percentageAmount,
                                                      string apexProduct, MerchantCommissionConfig item, string counterParty, string vcpValue,
                                                      string mcpValue, List<Commission> mappedList)
        {
            mappedList.Add(new Commission
            {
                CommissionType = commissionType,
                ProductCode = apexProduct,
                PercentageAmount = percentageAmount,
                PremiumPercentageAmount = apexProduct == VL ? vcpValue : (apexProduct == ML ? mcpValue : percentageAmount),
                FlatAmount = flatAmount,
                PremiumFlatAmount = premiumFlatAmount,
                MinCommAmount = Zero,
                MaxCommAmount = AppConstants.MaxValue,
                ValidFrom = DateTime.UtcNow.ToString(ApexRequestTransformer.Dateformat.DateFormatter),
                ValidTo = MaxExpiryDate,
                IsInterchange = item.IsInterchange == IsInter ? One : Zero,
                TransactionType = Entity.TransactionType,
                VatPercentage = counterParty == CounterParty.KSA ? AppConstants.Vat : item.VatPercentage ?? 0
            });
        }

        public static string RemoveSpecialCharacters(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            input = Regex.Replace(input, @"[^a-zA-Z0-9\s]", string.Empty);
            return input.TrimEnd();
        }
        public static string GetSubstring(string input, int startIndex, int length)
        {
            if (input == null)
            {
                return string.Empty;
            }

            if (startIndex + length > input.Length)
            {
                return input;
            }

            return input.Substring(startIndex, length);
        }
        private static List<MerchantFee> MapApexFees(List<MerchantFee>? fee)
        {
            var mappedList = new List<MerchantFee>();

            if (fee!.Count <= 0)
            {
                return mappedList;
            }
            foreach (var item in fee)
            {
                mappedList.Add(new Common.Models.MerchantFee
                {
                    Frequency = item.Frequency,
                    Amount = item.Amount,
                    Type = item.Type,
                    InactivityAmountThreshold = item.InactivityAmountThreshold,
                    MerchantId = item.MerchantId,
                    PaymentMethod = item.PaymentMethod,
                    ReportingOnly = item.ReportingOnly,
                    Sign = item.Sign,
                });
            }

            return mappedList;
        }

        private static string GetSettlementCurrencyValue(string settlementCurrency, string counterparty)
        {
            string settlementCurrencyValue = string.Empty;

            if (!string.IsNullOrEmpty(settlementCurrency))
            {
                return ISO.CurrencyCodes.NumericCodes!.GetValueOrDefault(settlementCurrency)!.ToString();
            }
            else if (counterparty == CounterParty.KSA)
            {
                return ISO.CurrencyCodes.NumericCodes!.GetValueOrDefault("SAR")!.ToString();
            }

            return settlementCurrencyValue;
        }

        private static string GetEconomicActivityValue(string acquiringLedger, string counterparty)
        {
            if (counterparty == CounterParty.KSA)
            {
                var mappedList = ApexRequestTransformer.BicMapper.CheckMapping();
                var matchedItem = mappedList.Find(item => item.AcquiringLedger == acquiringLedger);

                if (matchedItem != null && matchedItem.AcquiringLedger == "SABB")
                {
                    return EconomicActivity.Acquirer;
                }
                return EconomicActivity.Aggregator;
            }

            return EconomicActivity.Aggregator;
        }

        private static string GetPayoutSourceBankValue(string acquiringLedger, string counterparty)
        {
            if (counterparty == CounterParty.KSA)
            {
                var mappedList = ApexRequestTransformer.BicMapper.CheckMapping();
                var matchedItem = mappedList.Find(item => item.AcquiringLedger == acquiringLedger);
                if (matchedItem != null)
                {
                    return matchedItem.BIC!;
                }
                return string.Empty;
            }
            return string.Empty;
        }

        private static string GetCommercialRegistrationNumber(string? registrationNumber, string? unifiedId, string counterparty)
        {
            if (counterparty == CounterParty.KSA)
            {
                if (string.IsNullOrEmpty(registrationNumber))
                {
                    return unifiedId!;
                }
                return GetSubstring(registrationNumber, 0, 20);
            }
            return GetSubstring(registrationNumber!, 0, 20);
        }
    }
}