﻿using Common.Constants;
using Common.Models;
using Common.Models.Configuration;
using Common.Models.Products;
using Common.Services;
using Geidea.Utils.Messaging.Base;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Services.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Services.Orchestration;

public class ProductCreate : IProductCreate
{
    private readonly ILogger<ProductCreate> logger;
    private readonly IApexService apexservice;
    private readonly IApexTerminalClient apexTerminalClient;

    private readonly IServiceScopeFactory serviceScopeFactory;
    private readonly IProviderService providerService;

    public ProductCreate(ILogger<ProductCreate> logger, IApexService apexService, IProviderService providerService, IServiceScopeFactory serviceScopeFactory, IApexTerminalClient apexTerminalClient)
    {
        this.logger = logger;
        this.apexservice = apexService;
        this.providerService = providerService;
        this.serviceScopeFactory = serviceScopeFactory;
        this.apexTerminalClient = apexTerminalClient;
    }

    public async Task ProductMessageCreator(GenericMessage<Product> product)
    {
        try
        {
            var providers = await providerService.GetProviderQueueConfiguration(new QueueConfigSearchFilter { Action = AppConstants.Actions.Create, Entity = AppConstants.Entity.Merchant });
            foreach (var provider in providers)
            {
                using var scope = serviceScopeFactory.CreateScope();
                if (provider.ProviderCode == AppConstants.Providers.Apex)
                {
                    var apexService = scope.ServiceProvider.GetRequiredService<IApexService>();
                }

            }
        }
        catch (Exception exception)
        {
            logger.LogError("Message failed to process. Error: {ErrorMessage}", exception.Message);
            throw;
        }
    }
}