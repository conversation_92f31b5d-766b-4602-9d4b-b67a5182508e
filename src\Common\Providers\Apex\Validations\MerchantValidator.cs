﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class MerchantValidator : AbstractValidator<Merchant>
{
    public MerchantValidator()
    {
        RuleFor(m => m.MerchantId)
          .NotEmpty()
          .WithErrorCode(Errors.MerchantIdRequired.Code)
          .WithMessage(Errors.MerchantIdRequired.Message);

        RuleFor(m => m.MerchantType)
            .NotEmpty()
            .WithErrorCode(Errors.MerchantTypeRequired.Code)
            .WithMessage(Errors.MerchantTypeRequired.Message)
            .Length(1, 3)
            .WithErrorCode(Errors.MerchantTypeLength.Code)
            .WithMessage(Errors.MerchantTypeLength.Message);

        RuleFor(m => m.MerchantStatus)
            .NotEmpty()
            .WithErrorCode(Errors.MerchantStatusRequired.Code)
            .WithMessage(Errors.MerchantStatusRequired.Message)
            .Length(1, 20)
            .WithErrorCode(Errors.MerchantStatusLength.Code)
            .WithMessage(Errors.MerchantStatusLength.Message);

        RuleFor(x => x.MerchantDetails)
            .SetValidator(new MerchantDetailsValidator());

        RuleFor(x => x.AccountConfig)
            .SetValidator(new MerchantAccountConfigValidator());

        /*  RuleForEach(x => x.MerchantTerminals)
              .SetValidator(new MerchantTerminalValidator());
  */
        RuleForEach(x => x.Addresses)
            .SetValidator(x => new MerchantAddressValidator(x.Counterparty!));

        RuleForEach(x => x.PersonOfInterests)
            .SetValidator(new MerchantPersonOfInterestValidator());

        RuleForEach(x => x.BankAccounts)
            .SetValidator(new MerchantBankAccountResponseValidator());

        RuleForEach(x => x.CommissionTypes)
            .SetValidator(new MerchantCommissionConfigValidator());
    }
}