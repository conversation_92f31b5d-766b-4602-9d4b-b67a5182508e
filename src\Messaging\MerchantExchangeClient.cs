﻿using Common.Models;
using Common.Models.Messaging;
using Common.Services;
using FluentValidation;
using Geidea.Utils.Json;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;

namespace Messaging;

public class MerchantExchangeClient : MessageClient, IMerchantExchangeClient
{
    private const string MerchantCreateRoutingKey = "NexusBridge.Merchant.Create";
    private const string MerchantUpdateRoutingKey = "NexusBridge.Merchant.Update";
    private const string MerchantUpdateStatusRoutingKey = "NexusBridge.Merchant.UpdateStatus";
    private const string RetryQueueName = "NexusBridge.Merchant.Retry";
    private const string RetryRoutingKey = "NexusBridge.Merchant.Retry";
    private readonly IMerchantCreate merchantMessages;

    public event EventHandler<MerchantMessageBody>? OnMessageQueueCreate;

    public event EventHandler<MerchantMessageBody>? OnMessageQueueUpdate;

    public event EventHandler<MerchantMessageBody>? OnMessageQueueUpdateStatus;

    public MerchantExchangeClient(IHttpContextAccessor contextAccessor, ILogger<MessageClient> logger,
                                  IOptionsMonitor<RabbitMqOptions> rabbitMqOptions, IMerchantCreate merchantMessages) : base(contextAccessor, logger, rabbitMqOptions,
                new QueueSettings
                {
                    ExchangeName = "NexusBridge.Merchant.Recieve",
                    QueueName = "NexusBridge.Merchant.Recieve",
                    Durable = true
                })
    {
        this.logger = logger;
        this.merchantMessages = merchantMessages;
    }

    public override void OnMessageReciving(object? model, BasicDeliverEventArgs ea)
    {
        logger.LogInformation("OnMessageReciving started - DeliveryTag: {DeliveryTag}, RoutingKey: {RoutingKey}", ea.DeliveryTag, ea.RoutingKey);
        try
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            logger.LogInformation("Message body extracted, length: {MessageLength}", message.Length);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            logger.LogInformation("Message received on queue {QueueName} and Routing Key {RoutingKey} and Exchange {Exchange}", queue.QueueName, queue.RoutingKey, queue.ExchangeName);
            logger.LogInformation("Event Routing Key {EventRoutingKey}", ea.RoutingKey);
            switch (ea.RoutingKey)
            {
                case MerchantCreateRoutingKey:
                    {
                        logger.LogInformation("Entering MerchantCreateRoutingKey case");
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message, options);
                        logger.LogInformation("JSON deserialization successful, CorrelationId: {CorrelationId}", resultMessageBody.Header.CorrelationId);
                        RabbitMqHelper.AddCorrelationIdToLogger(resultMessageBody.Header.CorrelationId);
                        logger.LogInformation("CorrelationId added to logger");
                        OnMessageQueueCreate?.Invoke(this, new MerchantMessageBody(resultMessageBody.Data));
                        logger.LogInformation("Processing MerchantCreate message on queue {QueueName} and Routing Key {RoutingKey}", queue.QueueName, queue.RoutingKey);
                        logger.LogInformation("Invoke Merchant Message Creator");
                        logger.LogInformation("MerchantData {resultMessageBody}", resultMessageBody);
                        merchantMessages.MerchantMessageCreator(resultMessageBody);
                        break;
                    }
                case MerchantUpdateRoutingKey:
                    {
                        logger.LogInformation("Entering MerchantUpdateRoutingKey case");
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message, options);
                        logger.LogInformation("JSON deserialization successful for Update, CorrelationId: {CorrelationId}", resultMessageBody.Header.CorrelationId);
                        OnMessageQueueUpdate?.Invoke(this, new MerchantMessageBody(resultMessageBody.Data));
                        logger.LogInformation("MerchantUpdate event triggered");
                        break;
                    }
                case MerchantUpdateStatusRoutingKey:
                    {
                        logger.LogInformation("Entering MerchantUpdateStatusRoutingKey case");
                        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message, options);
                        logger.LogInformation("JSON deserialization successful for UpdateStatus, CorrelationId: {CorrelationId}", resultMessageBody.Header.CorrelationId);
                        OnMessageQueueUpdateStatus?.Invoke(this, new MerchantMessageBody(resultMessageBody.Data));
                        logger.LogInformation("MerchantUpdateStatus event triggered");
                        break;
                    }

                default:
                    {
                        logger.LogError("Invalid routing key: {RoutingKey}", ea.RoutingKey);
                        break;
                    }
            }
        }
        catch (ValidationException vex)
        {
            // Don't retry validation errors
            logger.LogError(vex, "Validation failed for message on queue {QueueName}. Message will not be retried.", queue.QueueName);

            // Optionally, you can log the validation errors
            foreach (var error in vex.Errors)
            {
                logger.LogError("Property: {PropertyName}, Error: {ErrorMessage}", error.PropertyName, error.ErrorMessage);
            }

            // Acknowledge message to remove it from queue
            Channel?.BasicAck(ea.DeliveryTag, false);
        }
        catch (Exception exception)
        {
            logger.LogError(exception, "Failed to process message received on queue {QueueName}", queue.QueueName);
            SendToRetryQueue(ea.Body, ea.RoutingKey);
        }
    }

    private void SendToRetryQueue(ReadOnlyMemory<byte> body, string routingKey)
    {
        try
        {
            var properties = Channel!.CreateBasicProperties();
            properties.Headers = new Dictionary<string, object>
            {
                { "x-retry-count", 3 } ,
                { "x-message-ttl", 30000 }
            };

            Channel.BasicPublish(queue.ExchangeName, RetryRoutingKey, properties, body);
            logger.LogInformation("Message sent to retry queue with routing key {RoutingKey}", RetryRoutingKey);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send message to retry queue");
        }
    }

    public override void Connect()
    {
        base.Connect();
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, MerchantCreateRoutingKey);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, MerchantUpdateRoutingKey);
        Channel.QueueBind(queue.QueueName, queue.ExchangeName, MerchantUpdateStatusRoutingKey);

        Channel!.QueueDeclare(RetryQueueName, durable: true, exclusive: false, autoDelete: false, arguments: null);
        Channel.QueueBind(RetryQueueName, queue.ExchangeName, RetryRoutingKey);
        ReciveMessageFromQueue(OnMessageReciving);
        StartRetryQueueConsumer();
    }

    public void StartRetryQueueConsumer()
    {
        var consumer = new EventingBasicConsumer(Channel);
        consumer.Received += (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var properties = ea.BasicProperties;
            var retryCount = properties.Headers.ContainsKey("x-retry-count") ? (int)properties.Headers["x-retry-count"] : 0;
            var routingKey = ea.RoutingKey;

            try
            {
                ProcessMessage(body, routingKey);
                Channel!.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Processing failed");

                if (retryCount < 3)
                {
                    Channel.BasicPublish(
                        exchange: "NexusBridge.Merchant.Exchange",
                        routingKey: RetryRoutingKey,
                        basicProperties: properties,
                        body: body
                    );

                    Channel!.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                }
                else
                {
                    logger.LogWarning("Message failed after retries.");
                    Channel!.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: false);
                }
            }
        };

        Channel.BasicConsume(queue: RetryQueueName, autoAck: false, consumer: consumer);
    }

    private void ProcessMessage(byte[] body, string routingKey)
    {
        var message = Encoding.UTF8.GetString(body);
        var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        var resultMessageBody = Json.Deserialize<GenericMessage<Merchant>>(message, options);

        switch (routingKey)
        {
            case RetryRoutingKey:
                OnMessageQueueCreate?.Invoke(this, new MerchantMessageBody(resultMessageBody.Data));
                merchantMessages.MerchantMessageCreator(resultMessageBody);
                break;
            default:
                logger.LogError("Invalid routing key: {RoutingKey}", routingKey);
                break;
        }
    }
}
