﻿using Common.Exceptions;
using Common.Models;
using FluentValidation;

namespace Common.Providers.Apex.Validations;

public class MerchantCommissionConfigValidator : AbstractValidator<MerchantCommissionConfig>
{
    public MerchantCommissionConfigValidator()
    {
        RuleFor(x => x.ProductCode)
            .NotEmpty()
            .WithErrorCode(Errors.ProductCodeRequired.Code)
            .WithMessage(Errors.ProductCodeRequired.Message);

        RuleFor(x => x.CommissionType)
            .NotEmpty()
            .WithErrorCode(Errors.CommissionTypeRequired.Code)
            .WithMessage(Errors.CommissionTypeRequired.Message);

        //RuleFor(x => x.Value)
        //    .GreaterThan(0)
        //    .WithErrorCode(Errors.ValueGreaterThanZero.Code)
        //    .WithMessage(Errors.ValueGreaterThanZero.Message);
    }
}