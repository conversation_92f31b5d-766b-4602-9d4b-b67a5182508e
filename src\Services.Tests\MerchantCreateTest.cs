﻿using Common.Constants;
using Common.Services;
using FluentAssertions;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Messaging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Channels;
using static Common.Constants.AppConstants;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Configuration;
using Services.Providers.Apex;
using Services.Orchestration;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json.Linq;
using Services.Helper;
using Services.Providers;
using System.Net.Http;
using Messaging.Publishers;

namespace Services.Tests
{
    public class MerchantCreateTest
    {
        private readonly MerchantCreate messageClient;
        private readonly ILogger<MerchantCreate> logger = Substitute.For<ILogger<MerchantCreate>>();

        private readonly IProviderService _providerService = Substitute.For<IProviderService>();

        private readonly IApexService _apexService;
        private readonly IServiceScope _serviceScope;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IApexResponsePublisherClient apexResponsePublisherClient;


        public MerchantCreateTest()
        {
            apexResponsePublisherClient = Substitute.For<IApexResponsePublisherClient>();
            _apexService = Substitute.For<IApexService>();
            _serviceScopeFactory = Substitute.For<IServiceScopeFactory>();
            _serviceScope = Substitute.For<IServiceScope>();
            _serviceScopeFactory.CreateScope().Returns(_serviceScope);
            _serviceScope.ServiceProvider.Returns(new ServiceProviderMock(_apexService));
            _apexService.CreateMerchant(Arg.Any<GenericMessage<Merchant>>(), Arg.Any<List<ProviderQueueConfigResponseModel>>())
       .Returns(Task.CompletedTask);
            messageClient = new MerchantCreate(logger, _providerService, _serviceScopeFactory, apexResponsePublisherClient);
        }

        [Test]
        public async Task MerchantMessageCreator_ShouldCallCreateMerchant_WhenEntityIsMerchant()
        {
            // Arrange
            var merchantMessage = new GenericMessage<Merchant>
            {
                Data = new Merchant
                {
                    Counterparty = "GEIDEA_UAE",
                    MerchantDetails = new Common.Models.MerchantDetails
                    {
                        BusinessId = "BIZ123",
                        MID = "MID123"
                    },
                    MerchantTerminals = new List<Common.Models.MerchantTerminal>
                    {
                        new Common.Models.MerchantTerminal
                        {
                            OrderNumber = "ORD123"
                        }
                    }
                }
            };
            var providers = new List<ProviderQueueConfigResponseModel>
            {
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, CounterParty =  "GEIDEA_UAE" , APIBaseUrl="https://10.245.88.105:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,CounterParty =  "GEIDEA_UAE" ,  APIBaseUrl="https://10.245.88.105:8443/api", Action = "Get", Entity = "MERCHANT",TargetURL = "/GetMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, CounterParty =  "GEIDEA_UAE" ,  APIBaseUrl="https://10.245.88.105:8443/api",Action = "Create", Entity = "PRODUCT",TargetURL = "/createTerminal" },

            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,  CounterParty =  "GEIDEA_SAUDI" , APIBaseUrl="https://10.57.0.131:8443/api",Action = "Create", Entity = "MERCHANT",TargetURL = "/merchant-inquiry/createMerchant" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex, CounterParty =  "GEIDEA_SAUDI" ,  APIBaseUrl="https://10.57.0.131:8443/api",Action = "Get", Entity = "MERCHANT",TargetURL = "/merchant-onboarding/getMerchantInformation" },
            new ProviderQueueConfigResponseModel { ProviderCode = AppConstants.Providers.Apex,CounterParty =  "GEIDEA_SAUDI" ,  APIBaseUrl = "https://10.57.0.131:8443/api", Action = "Create", Entity = "MERCHANT",TargetURL = "/merchant-inquiry/createTerminal" },
            };

            _providerService.GetProviderQueueConfiguration(Arg.Any<QueueConfigSearchFilter>())
           .Returns(Task.FromResult(providers));

            // Act
            await messageClient.MerchantMessageCreator(merchantMessage);

            // Assert
            await _apexService.Received(1).CreateMerchant(Arg.Any<GenericMessage<Merchant>>(), Arg.Any<List<ProviderQueueConfigResponseModel>>());

        }

        private class ServiceProviderMock : IServiceProvider
        {
            private readonly IApexService _apexService;

            public ServiceProviderMock(IApexService apexService)
            {
                _apexService = apexService;
            }

            public object GetService(Type serviceType)
            {
                if (serviceType == typeof(IApexService))
                {
                    return _apexService;
                }
                return null;
            }
        }

    }
}