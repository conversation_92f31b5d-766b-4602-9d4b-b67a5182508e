﻿using Common.Constants;
using Common.Models.Messaging;
using Common.Services;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Messaging.Publishers
{
    public class ApexResponsePublisherClient : MessageClient, IApexResponsePublisherClient
    {
        public ApexResponsePublisherClient(IHttpContextAccessor contextAccessor, ILogger<MessageClient> logger, IOptionsMonitor<RabbitMqOptions> rabbitMqOptions)
            : base(contextAccessor, logger, rabbitMqOptions, new QueueSettings
            {
                MessageSender = AppConstants.RabbitMqSenders.ApexResponseSender,
                ExchangeName = AppConstants.RabbitMqExchanges.ApexResponseExchange,
                QueueName = AppConstants.RabbitMqQueues.ApexResponseQueue,
                RoutingKey = AppConstants.RabbitMqRoutingKeys.ApexResponseRoutingKey,
                Durable = true
            })
        {
        }

        public virtual void SendApexResponseMessage(ApexResponse apexResponse)
        {
            try
            {
                if (connection == null || !connection.IsOpen)
                {
                    Connect();
                }

                var settings = new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented,
                    ContractResolver = new DefaultContractResolver()
                };

                logger.LogInformation("Sending ApexResponse message payload {payload} to queue {queueName}", JsonConvert.SerializeObject(apexResponse, settings), queue.QueueName);

                SendMessage(apexResponse);

                logger.LogInformation("ApexResonse Message has been Published Successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to send ApexResponse message");
                throw;
            }
        }
    }
}
