﻿using Common.Constants;
using Common.Models.Apex;
using Common.Models.Configuration;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Services.Helper.Interface;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services.Providers.Apex
{
    public class ApexToken : IApexToken
    {
        private readonly ILogger<ApexService> logger;
        private readonly IRequestLogService requestLogService;
        private readonly IHttpClientWrapper httpClientWrapper;
        private readonly IConfiguration configuration;
        private const string RequestIdHeader = "Request-ID";

        public ApexToken(ILogger<ApexService> logger, IRequestLogService requestLogService,
                         IConfiguration configuration, IHttpClientWrapper httpClientWrapper)
        {
            this.logger = logger;
            this.requestLogService = requestLogService;
            this.httpClientWrapper = httpClientWrapper;
            this.configuration = configuration;
        }

        public async Task<TokenResponse> GetTokenAsync(ProviderQueueConfigResponseModel providerConfig, MerchantAction merchantPayload, Guid? correlationId, string? mid, string entity)
        {
            logger.LogInformation("Generating token for {@providerCode} request", providerConfig.ProviderCode);

            var tokenResponse = new TokenResponse();
            var apexSettings = configuration.GetSection("Apex").Get<ApexSettings>();
            var credentials = $"{apexSettings.ClientId}:{apexSettings.ClientSecret}";
            var apiKey = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
            var url = $"{providerConfig.APIBaseUrl}/generate/token";
            var authorizationHeader = $"Basic {apiKey}";
            var contentType = "application/x-www-form-urlencoded";
            var grantType = "client_credentials";

            HttpContent content = new FormUrlEncodedContent(new[] { new KeyValuePair<string, string>("grant_type", grantType) })
            {
                Headers = { ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType) }
            };

            Dictionary<string, string>? headers = new() { { "Authorization", authorizationHeader } };

            try
            {
                var response = await httpClientWrapper.SendHttpPostRequest(url, content, headers);
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    tokenResponse = Json.Deserialize<TokenResponse>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                }
                else
                {
                    logger.LogCritical("Error Failed to retrieve token {providerCode}. Error was {StatusCode} {@responseBody}", providerConfig.ProviderCode, (int)response.StatusCode, response.ReasonPhrase);
                    throw new PassthroughException(response);
                }
            }
            catch (Exception ex)
            {
                logger.LogCritical("Error Failed to retrieve token. Error was {@responseBody}", ex);

                await requestLogService.UpdateRequestLogAsync(new Models.RequestLog()
                {
                    CorrelationId = correlationId,
                    ParentCorrelationId = correlationId,
                    EntityId = mid ?? string.Empty,
                    Status = AppConstants.Status.Failed,
                    Entity = entity,
                    EntryDate = DateTime.UtcNow,
                    RequestId = RequestIdHeader,
                    RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantPayload))),
                    RequestType = AppConstants.Actions.Create,
                    ResponseMetaData = ex.Message,
                });
                throw new Exception("Failed to retrieve token", ex);
            }
            return tokenResponse;
        }

    }
}