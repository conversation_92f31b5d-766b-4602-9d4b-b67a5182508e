﻿using System;

namespace Common.Models;

public class MerchantDetails
{
    public Guid MerchantDetailsId { get; set; }
    public string? BusinessDomain { get; set; }
    public string? LegalName { get; set; }

    public string? BusinessId { get; set; } = string.Empty;
    public string? MerchantSegment { get; set; }
    public string? LegalNameAr { get; set; }
    public string? TradingName { get; set; }
    public string? Nickname { get; set; }
    public string? TradingCurrency { get; set; }
    public DateTime FoundationDate { get; set; }
    public string? MunicipalLicenseNumber { get; set; }
    public bool VatAppliedFlag { get; set; }
    public bool TaxExempt { get; set; }
    public string? AdditionalTradingInformation { get; set; }
    public string? MIDMerchantReference { get; set; }
    public string? ReferralChannel { get; set; }

    public string? Website { get; set; }
    public decimal AnnualTurnover { get; set; }
    public string? Region { get; set; }
    public string? DefaultLanguage { get; set; }
    public bool CheckoutCompleted { get; set; }
    public string? UnifiedId { get; set; }
    public bool TspModel { get; set; }
    public string? AcquiringLedger { get; set; }
    public string? BusinessType { get; set; }
    public string? OutletType { get; set; }
    public string? AlternativeMerchantName { get; set; }
    public string? StoreName { get; set; }
    public string? AlternativeStoreName { get; set; }
    public string? RiskLevel { get; set; }
    public string? MCC { get; set; }
    public string? MID { get; set; }
    public string? RegistrationNumber { get; set; }
    public string? CompanyPhoneNumber { get; set; }
    public int MaxMonthlyTransaction { get; set; }
    public int HighestSingleTransaction { get; set; }
    public int SalesAgent { get; set; }
    public int SalesManager { get; set; }
    public string? VatNumber { get; set; }
    public string? ClientNumber { get; set; }
    public string? UnifiedNumber { get; set; }

    public DateTime? TLIssueDate { get; set; }
    public DateTime? TLExpiryDate { get;set; }
}