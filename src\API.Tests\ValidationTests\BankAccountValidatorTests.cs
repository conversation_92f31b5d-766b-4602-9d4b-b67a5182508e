﻿using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace API.Tests.ValidationTests;

public class BankAccountValidatorTests
{
    private BankAccountValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new BankAccountValidator();
    }

    [Test]
    public void Should_Have_Error_When_IBAN_Is_Empty()
    {
        var model = new BankAccount { IBAN = string.Empty };
        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.IBAN)
              .WithErrorCode(Errors.IBANRequired.Code);
    }

    [Test]
    public void Should_Not_Have_Error_When_Model_Is_Valid()
    {
        var model = new BankAccount
        {
            CountryCode = "US",
            AccountHolderName = "John Doe",
            IBAN = "**********************",
            AccountName = "John's Account",
            ValidFrom = DateTime.UtcNow.AddDays(-1)
        };
        var result = _validator.TestValidate(model);
        result.ShouldNotHaveAnyValidationErrors();
    }
}
