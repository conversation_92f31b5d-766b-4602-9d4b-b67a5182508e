﻿using AutoMapper;
using Common.Constants;
using Common.Models.Apex;
using Common.Models.Configuration;
using Common.Options;
using Common.Services;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Services.Helper;
using Services.Helper.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Services.Models;
using Common.Exceptions;
using static Common.Constants.AppConstants;

namespace Services.Providers.Apex
{
    public class ApexBusinessAccountClient : IApexBusinessAccountClient
    {
        private readonly ILogger<ApexService> logger;
        private readonly HttpClient httpClient;
        private readonly IProviderService providerService;
        private readonly IRequestLogService requestLogService;
        private readonly IMapper mapper;
        private readonly IHttpContextAccessor contextAccessor;
        private readonly string correlationId;
        private readonly IOptionsMonitor<UrlSettings> urlOptions;
        private readonly IHttpRequestExecutorService httpRequestExecutorService;
        private readonly IHttpClientWrapper httpClientWrapper;
        private readonly IApexToken token;
        private readonly IRequestHeaders requestHeaders;
        private readonly IConfiguration configuration;
        private const string RequestIdHeader = "Request-ID";
        private const string ApplicationLanguageHeader = "X-ApplicationLanguage";
        private const string CounterpartyCodeHeader = "X-CounterpartyCode";

        public ApexBusinessAccountClient(
            HttpClient httpClient,
            ILogger<ApexService> logger,
            IProviderService providerService,
            IRequestLogService requestLogService,
            IMapper mapper,
            IHttpContextAccessor contextAccessor,
            IOptionsMonitor<UrlSettings> urlOptions,
            IHttpRequestExecutorService httpRequestExecutorService, IConfiguration configuration, IRequestHeaders requestHeaders, IHttpClientWrapper httpClientWrapper, IApexToken token)
        {
            this.logger = logger;
            this.httpClient = httpClient;
            this.providerService = providerService;
            this.requestLogService = requestLogService;
            this.httpClientWrapper = httpClientWrapper;
            this.mapper = mapper;
            this.contextAccessor = contextAccessor;
            this.urlOptions = urlOptions;
            this.httpRequestExecutorService = httpRequestExecutorService;
            var headerCorrelationId = contextAccessor.HttpContext?.Request.Headers[Geidea.Utils.Common.Constants.CorrelationHeaderName];
            correlationId = headerCorrelationId.HasValue ? headerCorrelationId.Value.ToString() : Guid.Empty.ToString();
            this.configuration = configuration;
            this.requestHeaders = requestHeaders;
            this.token = token;
        }

        public async Task CreateBusinessOrAccount(GenericMessage<Common.Models.Merchant> merchantDetail, List<ProviderQueueConfigResponseModel> providerConfig, MerchantAction? merchantPayload, TokenResponse responseData)
        {
            var apiUrl = providerConfig.Find(x => x.Action == AppConstants.Actions.Create && x.Entity == AppConstants.Entity.Merchant);
            var requestUri = $"{apiUrl!.APIBaseUrl}{apiUrl.TargetURL}";

            using (logger.BeginScope($"{nameof(CreateBusinessOrAccount)}({requestUri})"))
            {
                var merchant = merchantDetail.Data;
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new LowercaseContractResolver(),
                    Formatting = Formatting.Indented
                };

                var payload = JsonConvert.SerializeObject(merchantPayload, settings);
                logger.LogInformation("Calling Apex Service to create Business or Account");

                try
                {
                    await SendCreateMerchantRequest(merchantDetail, merchantPayload, responseData, requestUri);
                }
                catch (Exception ex) when (ex.Message.Contains(AppConstants.TokenError))
                {
                    try
                    {
                        logger.LogWarning("Token expired for MID: {MID}, attempting to refresh and retry", merchant.MerchantDetails.MID);

                        var newAccessToken = await token.GetTokenAsync(providerConfig.FirstOrDefault()!, merchantPayload!, merchantDetail.Header.CorrelationId, merchantDetail.Data.MerchantDetails.MID, AppConstants.Entity.Merchant);

                        // Retry the request with the new access token
                        await SendCreateMerchantRequest(merchantDetail, merchantPayload, newAccessToken, requestUri);

                    }
                    catch (Exception Retryex)
                    {
                        logger.LogCritical("Failed to create Business or Account in APEX after token refresh for MID: {MID}. Error: {Error}", merchant.MerchantDetails.MID, Retryex.Message);

                        await requestLogService.CreateRequestLogAsync(CreateFailedRequestLog(merchant, merchantDetail, payload, Retryex));

                        throw new FederationException(
                            level: merchantPayload?.Merchant?.MerchantLevel == MerchantTypes.AccountLevel ? "Account" : "Business",
                            provider: "APEX",
                            message: $"APEX API call failed after token refresh for MID {merchant.MerchantDetails.MID}: {Retryex.Message}",
                            innerException: Retryex
                        );
                    }
                }
                catch (Exception ex)
                {
                    logger.LogCritical("Failed to create Business or Account in APEX for MID: {MID}. Error: {Error}", merchant.MerchantDetails.MID, ex.Message);

                    await requestLogService.CreateRequestLogAsync(CreateFailedRequestLog(merchant, merchantDetail, payload, ex));

                    throw new FederationException(
                        level: merchantPayload?.Merchant?.MerchantLevel == MerchantTypes.AccountLevel ? "Account" : "Business",
                        provider: "APEX",
                        message: $"APEX API call failed for MID {merchant.MerchantDetails.MID}: {ex.Message}",
                        innerException: ex
                    );
                }
            }
        }

        private async Task SendCreateMerchantRequest(GenericMessage<Common.Models.Merchant> merchantDetail, MerchantAction? merchantPayload, TokenResponse responseData, string requestUri)
        {
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new LowercaseContractResolver(),
                Formatting = Formatting.Indented
            };

            var payload = JsonConvert.SerializeObject(merchantPayload, settings);

            HttpContent content = new StringContent(payload, Encoding.UTF8)
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
            };

            Dictionary<string, string>? headers = new()
            {
                { "Authorization", "Bearer "  + responseData.access_token }
            };

            var merchant = merchantDetail.Data;

            logger.LogInformation("Calling APEX to create create Business or Account Request UrI: {@requestUri}", requestUri);
            logger.LogInformation("Calling APEX to create create Business or Account Payload: {@payload}", payload);

            HttpResponseMessage response = await httpClientWrapper.SendHttpPostRequest(requestUri, content, headers);
            var responseContent = await response.Content.ReadAsStringAsync();

            CreateMerchantResponse responseBody = new();
            if (!string.IsNullOrEmpty(responseContent))
            {
                responseBody = JsonConvert.DeserializeObject<CreateMerchantResponse>(responseContent);
            }

            if (!response.IsSuccessStatusCode
            || (!string.IsNullOrEmpty(responseBody.ResponseCode) && !responseBody.ResponseCode.Equals("00") && !responseBody.ResponseDescription!.Contains("already exists")))
            {
                logger.LogCritical("Error when calling Apex Create Merchant Message. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseContent);
                throw new PassthroughException(response);
            }

            logger.LogInformation("Successfull calling Apex to create Merchant Message. StatusCode: {StatusCode} Response: {@responseBody}", (int)response.StatusCode, responseContent);

            await requestLogService.CreateRequestLogAsync(CreateSuccessRequestLog(merchant, responseContent, merchantDetail, merchantPayload));
        }

        private RequestLog CreateFailedRequestLog(Common.Models.Merchant merchant, GenericMessage<Common.Models.Merchant> merchantDetail, string payload, Exception ex)
        {
            return new RequestLog
            {
                CorrelationId = merchantDetail.Header.CorrelationId,
                ParentCorrelationId = merchantDetail.Header.CorrelationId,
                EntityId = merchant.MerchantDetails?.MID ?? string.Empty,
                Status = AppConstants.Status.Failed,
                Entity = AppConstants.Entity.Merchant,
                EntryDate = DateTime.UtcNow,
                RequestId = RequestIdHeader,
                RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(payload)),
                RequestType = AppConstants.Actions.Create,
                ResponseMetaData = ex.Message,
            };
        }
        private RequestLog CreateSuccessRequestLog(Common.Models.Merchant merchant, string responseContent, GenericMessage<Common.Models.Merchant> merchantDetail, MerchantAction? merchantPayload)
        {
            return new RequestLog
            {
                CorrelationId = merchantDetail.Header.CorrelationId,
                ParentCorrelationId = merchantDetail.Header.CorrelationId,
                EntityId = merchant.MerchantDetails?.MID ?? string.Empty,
                Status = AppConstants.Status.Completed,
                Entity = AppConstants.Entity.Merchant,
                EntryDate = DateTime.UtcNow,
                RequestId = RequestIdHeader,
                RequestMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(merchantPayload))),
                RequestType = AppConstants.Actions.Create,
                ResponseMetaData = Convert.ToBase64String(Encoding.UTF8.GetBytes(responseContent)),
            };
        }
    }
}