﻿using Common.Exceptions;
using Common.Models;
using Common.Providers.Apex.Validations;
using FluentValidation.TestHelper;
using NUnit.Framework;
using System;

namespace API.Tests.ValidationTests
{
    public class MerchantDetailsValidatorTests
    {
        private MerchantDetailsValidator _validator;

        [SetUp]
        public void SetUp()
        {
            _validator = new MerchantDetailsValidator();
        }

        [Test]
        public void Should_Have_Error_When_LegalName_Is_Empty()
        {
            var model = new MerchantDetails { LegalName = string.Empty };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.LegalName)
                  .WithErrorCode(Errors.LegalNameRequired.Code);
        }

        [Test]
        public void Should_Have_Error_When_MCC_Exceeds_Max_Length()
        {
            var model = new MerchantDetails { MCC = new string('A', 51) };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.MCC)
                  .WithErrorCode(Errors.MCCMaxLength.Code);
        }

        [Test]
        public void Should_Have_Error_When_TLIssueDate_Is_In_The_Future()
        {
            var model = new MerchantDetails { TLIssueDate = DateTime.UtcNow.AddDays(1) };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.TLIssueDate)
                  .WithErrorCode(Errors.TLIssueDateFuture.Code);
        }

        [Test]
        public void Should_Have_Error_When_TLExpiryDate_Is_Less_Than_Or_Equal_To_TLIssueDate()
        {
            var model = new MerchantDetails
            {
                TLIssueDate = DateTime.UtcNow,
                TLExpiryDate = DateTime.UtcNow.AddDays(-1)
            };
            var result = _validator.TestValidate(model);
            result.ShouldHaveValidationErrorFor(x => x.TLExpiryDate)
                  .WithErrorCode(Errors.TLExpiryDateAfterRegistration.Code);
        }

        [Test]
        public void Should_Not_Have_Error_When_Model_Is_Valid()
        {
            var model = new MerchantDetails
            {
                LegalName = "Valid Legal Name",
                Nickname = "Valid Nickname",
                RegistrationNumber = "Valid Registration Number",
                MunicipalLicenseNumber = "Valid License Number",
                MCC = "12345",
                TLIssueDate = DateTime.UtcNow.AddYears(-1),
                TLExpiryDate = DateTime.UtcNow.AddYears(1)
            };
            var result = _validator.TestValidate(model);
            result.ShouldNotHaveAnyValidationErrors();
        }
    }
}