﻿using Common.Models.Apex;
using Common.Models.Configuration;
using Geidea.Utils.Messaging.Base;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IApexBusinessAccountClient
    {
        Task CreateBusinessOrAccount(GenericMessage<Common.Models.Merchant> merchantDetail, List<ProviderQueueConfigResponseModel> providerConfig, MerchantAction? merchantPayload, TokenResponse responseData);
    }
}