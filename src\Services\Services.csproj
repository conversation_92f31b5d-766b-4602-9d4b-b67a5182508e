﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    	<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>    
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Utils\**" />
    <EmbeddedResource Remove="Utils\**" />
    <None Remove="Utils\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Geidea.Utils.Common" Version="1.0.22" />
    <PackageReference Include="Geidea.Utils.Exceptions" Version="1.1.330" />
    <PackageReference Include="Geidea.Utils.Json" Version="1.1.251" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="ncrontab" Version="3.3.1" />
    <PackageReference Include="Polly" Version="7.2.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
    <ProjectReference Include="..\Messaging\Messaging.csproj" />
  </ItemGroup>

</Project>
